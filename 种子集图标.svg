<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .seed-bag { fill: #8d6e63; stroke: #5d4037; stroke-width: 2; }
      .bag-opening { fill: #a1887f; stroke: #6d4c41; stroke-width: 1; }
      .seed-large { fill: #4caf50; stroke: #2e7d32; stroke-width: 1.5; }
      .seed-medium { fill: #66bb6a; stroke: #388e3c; stroke-width: 1; }
      .seed-small { fill: #81c784; stroke: #4caf50; stroke-width: 0.8; }
      .premium-seed { fill: #2e7d32; stroke: #1b5e20; stroke-width: 2; }
      .seed-glow { fill: #c8e6c9; opacity: 0.6; }
      .sprouting { stroke: #4caf50; stroke-width: 1.5; fill: none; }
      .leaf { fill: #66bb6a; stroke: #4caf50; stroke-width: 0.5; }
      .text { font-family: Arial, sans-serif; font-size: 10px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
      .small-text { font-family: Arial, sans-serif; font-size: 7px; text-anchor: middle; fill: #4caf50; }
    </style>
    <filter id="seed-glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 种子袋 -->
  <ellipse cx="60" cy="85" rx="25" ry="20" class="seed-bag"/>
  <ellipse cx="60" cy="75" rx="20" ry="8" class="bag-opening"/>
  
  <!-- 袋子装饰线 -->
  <path d="M 45 80 Q 60 85 75 80" fill="none" stroke="#6d4c41" stroke-width="1"/>
  <path d="M 48 90 Q 60 95 72 90" fill="none" stroke="#6d4c41" stroke-width="1"/>
  
  <!-- 从袋子中散落的种子 -->
  <!-- 大种子（主要种子） -->
  <circle cx="45" cy="60" r="5" class="premium-seed" filter="url(#seed-glow)"/>
  <circle cx="75" cy="55" r="5" class="premium-seed" filter="url(#seed-glow)"/>
  <circle cx="60" cy="45" r="6" class="premium-seed" filter="url(#seed-glow)"/>
  
  <!-- 中等种子 -->
  <circle cx="35" cy="70" r="4" class="seed-large"/>
  <circle cx="85" cy="70" r="4" class="seed-large"/>
  <circle cx="50" cy="35" r="4" class="seed-large"/>
  <circle cx="70" cy="35" r="4" class="seed-large"/>
  
  <!-- 小种子 -->
  <circle cx="25" cy="80" r="3" class="seed-medium"/>
  <circle cx="95" cy="80" r="3" class="seed-medium"/>
  <circle cx="40" cy="25" r="3" class="seed-medium"/>
  <circle cx="80" cy="25" r="3" class="seed-medium"/>
  <circle cx="30" cy="50" r="2.5" class="seed-small"/>
  <circle cx="90" cy="50" r="2.5" class="seed-small"/>
  
  <!-- 种子发芽效果 -->
  <g transform="translate(60,45)">
    <!-- 主芽 -->
    <path d="M 0,6 Q -2,2 0,0 Q 2,2 0,6" class="sprouting"/>
    <!-- 小叶子 -->
    <ellipse cx="-1" cy="2" rx="2" ry="1" class="leaf"/>
    <ellipse cx="1" cy="3" rx="1.5" ry="0.8" class="leaf"/>
  </g>
  
  <g transform="translate(45,60)">
    <path d="M 0,5 Q -1,2 0,0" class="sprouting"/>
    <ellipse cx="-0.5" cy="1.5" rx="1" ry="0.5" class="leaf"/>
  </g>
  
  <g transform="translate(75,55)">
    <path d="M 0,5 Q 1,2 0,0" class="sprouting"/>
    <ellipse cx="0.5" cy="1.5" rx="1" ry="0.5" class="leaf"/>
  </g>
  
  <!-- 种子标识 -->
  <g transform="translate(60,45)">
    <text x="0" y="-12" style="font-size:6px; text-anchor:middle; fill:#1b5e20; font-weight:bold;">S*</text>
  </g>
  
  <g transform="translate(45,60)">
    <text x="0" y="-8" style="font-size:5px; text-anchor:middle; fill:#2e7d32;">S1</text>
  </g>
  
  <g transform="translate(75,55)">
    <text x="0" y="-8" style="font-size:5px; text-anchor:middle; fill:#2e7d32;">S2</text>
  </g>
  
  <!-- 种子数量标识 -->
  <g transform="translate(85,85)">
    <circle cx="0" cy="0" r="8" fill="#fff" stroke="#4caf50" stroke-width="1.5"/>
    <text x="0" y="2" style="font-size:6px; text-anchor:middle; fill:#2e7d32; font-weight:bold;">×5</text>
  </g>
  
  <!-- 影响力扩散效果 -->
  <circle cx="60" cy="45" r="15" class="seed-glow"/>
  <circle cx="45" cy="60" r="12" class="seed-glow"/>
  <circle cx="75" cy="55" r="12" class="seed-glow"/>
  
  <!-- 连接线显示网络效应 -->
  <line x1="45" y1="60" x2="60" y2="45" stroke="#81c784" stroke-width="1.5" opacity="0.7" stroke-dasharray="2,2"/>
  <line x1="60" y1="45" x2="75" y2="55" stroke="#81c784" stroke-width="1.5" opacity="0.7" stroke-dasharray="2,2"/>
  <line x1="75" y1="55" x2="85" y2="70" stroke="#81c784" stroke-width="1" opacity="0.5" stroke-dasharray="1,1"/>
  <line x1="45" y1="60" x2="35" y2="70" stroke="#81c784" stroke-width="1" opacity="0.5" stroke-dasharray="1,1"/>
  
  <!-- 标题 -->
  <text x="60" y="15" class="text">🌱 种子集合</text>
  <text x="60" y="115" class="small-text">影响力最大化种子节点</text>
</svg>
