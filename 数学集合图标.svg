<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .brace { fill: none; stroke: #4caf50; stroke-width: 3; }
      .node { fill: #66bb6a; stroke: #2e7d32; stroke-width: 1.5; }
      .best-node { fill: #4caf50; stroke: #1b5e20; stroke-width: 2; }
      .connection { stroke: #a5d6a7; stroke-width: 1; opacity: 0.7; }
      .set-symbol { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
      .text { font-family: Arial, sans-serif; font-size: 8px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
      .small-text { font-family: Arial, sans-serif; font-size: 6px; text-anchor: middle; fill: #4caf50; }
    </style>
  </defs>
  
  <!-- 左花括号 -->
  <path d="M 20 15 Q 15 15 15 20 Q 15 25 10 30 Q 15 35 15 40 Q 15 45 20 45" class="brace"/>
  
  <!-- 右花括号 -->
  <path d="M 100 15 Q 105 15 105 20 Q 105 25 110 30 Q 105 35 105 40 Q 105 45 100 45" class="brace"/>
  
  <!-- 种子节点（用小正方形表示，避免与圆形节点混淆） -->
  <rect x="26" y="26" width="6" height="6" rx="1" class="best-node"/>
  <rect x="40" y="22" width="5" height="5" rx="1" class="node"/>
  <rect x="54" y="28" width="5" height="5" rx="1" class="node"/>
  <rect x="68" y="24" width="5" height="5" rx="1" class="node"/>
  <rect x="82" y="30" width="5" height="5" rx="1" class="node"/>
  
  <!-- 连接线 -->
  <line x1="29" y1="29" x2="42" y2="24" class="connection"/>
  <line x1="42" y1="24" x2="56" y2="30" class="connection"/>
  <line x1="56" y1="30" x2="70" y2="26" class="connection"/>
  <line x1="70" y1="26" x2="84" y2="32" class="connection"/>
  <line x1="29" y1="29" x2="56" y2="30" class="connection"/>
  
  <!-- 节点标识 -->
  <text x="29" y="20" style="font-size:5px; text-anchor:middle; fill:#1b5e20; font-weight:bold;">s*</text>
  <text x="42" y="17" style="font-size:4px; text-anchor:middle; fill:#2e7d32;">s₁</text>
  <text x="56" y="23" style="font-size:4px; text-anchor:middle; fill:#2e7d32;">s₂</text>
  <text x="70" y="19" style="font-size:4px; text-anchor:middle; fill:#2e7d32;">s₃</text>
  <text x="84" y="25" style="font-size:4px; text-anchor:middle; fill:#2e7d32;">s₄</text>
  
  <!-- 集合符号 -->
  <text x="5" y="35" class="set-symbol">S =</text>
  
  <!-- 逗号分隔 -->
  <text x="35" y="40" style="font-size:8px; text-anchor:middle; fill:#666;">,</text>
  <text x="49" y="40" style="font-size:8px; text-anchor:middle; fill:#666;">,</text>
  <text x="63" y="40" style="font-size:8px; text-anchor:middle; fill:#666;">,</text>
  <text x="77" y="40" style="font-size:8px; text-anchor:middle; fill:#666;">,</text>
  
  <!-- 标题 -->
  <text x="60" y="10" class="text">📊 种子集合输出</text>
  <text x="60" y="55" class="small-text">S = {s*, s₁, s₂, s₃, s₄} - 最优影响力种子节点集</text>
</svg>
