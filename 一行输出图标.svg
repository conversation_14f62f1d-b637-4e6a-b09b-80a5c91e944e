<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .loop-end { fill: #ffecb3; stroke: #ff8f00; stroke-width: 2; }
      .stop-sign { fill: #f44336; stroke: #d32f2f; stroke-width: 1.5; }
      .arrow-flow { stroke: #4caf50; stroke-width: 3; }
      .output-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .seed-node { fill: #66bb6a; stroke: #2e7d32; stroke-width: 1.5; }
      .best-seed { fill: #4caf50; stroke: #1b5e20; stroke-width: 2; }
      .connection { stroke: #81c784; stroke-width: 1; opacity: 0.8; }
      .success-icon { fill: #4caf50; }
      .text { font-family: Arial, sans-serif; font-size: 9px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
      .small-text { font-family: Arial, sans-serif; font-size: 7px; text-anchor: middle; fill: #666; }
    </style>
    <marker id="flow-arrow" markerWidth="6" markerHeight="4" refX="5" refY="2" orient="auto">
      <polygon points="0 0, 6 2, 0 4" fill="#4caf50"/>
    </marker>
  </defs>
  
  <!-- 循环结束部分 -->
  <g transform="translate(30,30)">
    <!-- 循环轨迹 -->
    <circle cx="0" cy="0" r="18" fill="none" stroke="#ff8f00" stroke-width="2" stroke-dasharray="4,2" opacity="0.7"/>
    <!-- 停止标志 -->
    <polygon points="0,-8 6,-2 6,2 0,8 -6,2 -6,-2" class="stop-sign"/>
    <text x="0" y="2" style="font-size:5px; text-anchor:middle; fill:#fff; font-weight:bold;">END</text>
    <!-- 迭代标识 -->
    <text x="0" y="-25" class="small-text">Gen: 150</text>
  </g>
  
  <!-- 流向箭头1 -->
  <line x1="55" y1="30" x2="85" y2="30" class="arrow-flow" marker-end="url(#flow-arrow)"/>
  
  <!-- 收敛成功标识 -->
  <g transform="translate(100,30)">
    <circle cx="0" cy="0" r="12" fill="#4caf50" opacity="0.9"/>
    <path d="M -4,-2 L -1,3 L 5,-4" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round"/>
    <text x="0" y="-20" class="small-text">收敛成功</text>
  </g>
  
  <!-- 流向箭头2 -->
  <line x1="120" y1="30" x2="150" y2="30" class="arrow-flow" marker-end="url(#flow-arrow)"/>
  
  <!-- 输出种子集 -->
  <g transform="translate(170,30)">
    <!-- 输出容器 -->
    <rect x="-25" y="-15" width="50" height="30" rx="5" class="output-box"/>
    
    <!-- 种子节点 -->
    <circle cx="-15" cy="-5" r="3" class="seed-node"/>
    <circle cx="-5" cy="0" r="4" class="best-seed"/>
    <circle cx="5" cy="-5" r="3" class="seed-node"/>
    <circle cx="15" cy="5" r="3" class="seed-node"/>
    
    <!-- 连接线 -->
    <line x1="-15" y1="-5" x2="-5" y2="0" class="connection"/>
    <line x1="-5" y1="0" x2="5" y2="-5" class="connection"/>
    <line x1="-5" y1="0" x2="15" y2="5" class="connection"/>
    
    <!-- 种子标识 -->
    <text x="-5" y="-8" style="font-size:5px; text-anchor:middle; fill:#1b5e20;">S*</text>
    
    <!-- 输出标签 -->
    <rect x="-20" y="-12" width="12" height="6" rx="1" fill="#ffd700" stroke="#ff8f00" stroke-width="0.5"/>
    <text x="-14" y="-8" style="font-size:4px; text-anchor:middle; fill:#ff8f00;">OUTPUT</text>
    
    <text x="0" y="-22" class="small-text">最优种子集</text>
  </g>
  
  <!-- 流向箭头3 -->
  <line x1="200" y1="30" x2="230" y2="30" class="arrow-flow" marker-end="url(#flow-arrow)"/>
  
  <!-- 最终结果 -->
  <g transform="translate(250,30)">
    <!-- 奖杯简化版 -->
    <ellipse cx="0" cy="-3" rx="8" ry="5" fill="#ffd700" stroke="#ff8f00" stroke-width="1"/>
    <rect x="-5" y="2" width="10" height="4" rx="1" fill="#8d6e63"/>
    <text x="0" y="0" style="font-size:4px; text-anchor:middle; fill:#ff8f00;">★</text>
    
    <!-- 性能指标 -->
    <text x="0" y="15" style="font-size:6px; text-anchor:middle; fill:#2e7d32;">影响力: 95.2%</text>
    <text x="0" y="-18" class="small-text">算法完成</text>
  </g>
  
  <!-- 整体标题 -->
  <text x="150" y="12" class="text">🔄 循环结束 → ✅ 收敛成功 → 🏆 输出种子集 → 📊 最终结果</text>
  
  <!-- 底部说明 -->
  <text x="150" y="55" class="small-text">ANFDE-IM算法执行完成，输出最优影响力最大化种子节点集合</text>
</svg>
