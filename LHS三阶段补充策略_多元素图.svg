<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; text-anchor: middle; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .node-text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
      .small-text { font-family: Arial, sans-serif; font-size: 8px; text-anchor: middle; }
      .input { fill: #e1f5fe; stroke: #0277bd; stroke-width: 3; }
      .stage1 { fill: #fff3e0; stroke: #f57c00; stroke-width: 3; }
      .stage2 { fill: #fce4ec; stroke: #c2185b; stroke-width: 3; }
      .stage3 { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 3; }
      .output { fill: #e8f5e8; stroke: #2e7d32; stroke-width: 4; }
      .group-bg { fill: #f9f9f9; stroke: #666; stroke-width: 1; stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" class="title">LHS三阶段补充策略 - 多元素框架图</text>
  <text x="800" y="50" class="subtitle">区域均衡 × 桥节点补充 × 外围节点完善 = 全面覆盖</text>
  
  <!-- 输入多元素区域 -->
  <rect x="20" y="80" width="280" height="750" rx="10" class="group-bg"/>
  <text x="160" y="100" class="subtitle">输入多元素</text>
  
  <!-- 网络图G -->
  <rect x="40" y="120" width="240" height="80" rx="10" class="input"/>
  <text x="160" y="140" class="node-text">🌐 网络图G</text>
  <text x="160" y="155" class="small-text">📊 节点集合V</text>
  <text x="160" y="170" class="small-text">🔗 边集合E</text>
  <text x="160" y="185" class="small-text">📈 拓扑结构分析</text>
  
  <!-- 当前解集 -->
  <rect x="40" y="220" width="240" height="80" rx="10" class="input"/>
  <text x="160" y="240" class="node-text">🎯 当前解集</text>
  <text x="160" y="255" class="small-text">💎 已选节点</text>
  <text x="160" y="270" class="small-text">📋 解的质量</text>
  <text x="160" y="285" class="small-text">🔍 覆盖分析</text>
  
  <!-- 评分系统 -->
  <rect x="40" y="320" width="240" height="80" rx="10" class="input"/>
  <text x="160" y="340" class="node-text">📊 评分系统</text>
  <text x="160" y="355" class="small-text">⚖️ 综合中心性</text>
  <text x="160" y="370" class="small-text">🏆 节点重要性</text>
  <text x="160" y="385" class="small-text">📈 归一化分值</text>
  
  <!-- 桥节点集 -->
  <rect x="40" y="420" width="240" height="80" rx="10" class="input"/>
  <text x="160" y="440" class="node-text">🔗 桥节点集</text>
  <text x="160" y="455" class="small-text">🌉 关键连接</text>
  <text x="160" y="470" class="small-text">📊 Top 10%</text>
  <text x="160" y="485" class="small-text">⚡ 网络稳定性</text>
  
  <!-- 区域信息 -->
  <rect x="40" y="520" width="240" height="80" rx="10" class="input"/>
  <text x="160" y="540" class="node-text">🗺️ 区域信息</text>
  <text x="160" y="555" class="small-text">📏 直径路径</text>
  <text x="160" y="570" class="small-text">📊 距离分层</text>
  <text x="160" y="585" class="small-text">🎯 空间结构</text>
  
  <!-- 阶段1区域 -->
  <rect x="340" y="80" width="320" height="200" rx="10" class="group-bg"/>
  <text x="500" y="100" class="subtitle">阶段1: 区域均衡选择</text>
  
  <!-- 区域遍历 -->
  <rect x="360" y="120" width="130" height="70" rx="8" class="stage1"/>
  <text x="425" y="135" class="node-text">🔄 区域遍历</text>
  <text x="425" y="150" class="small-text">⚖️ 均衡选择</text>
  <text x="425" y="165" class="small-text">🎨 多样性保证</text>
  
  <!-- 质量筛选 -->
  <rect x="510" y="120" width="130" height="70" rx="8" class="stage1"/>
  <text x="575" y="135" class="node-text">🏆 质量筛选</text>
  <text x="575" y="150" class="small-text">📈 评分排序</text>
  <text x="575" y="165" class="small-text">⭐ 最优选择</text>
  
  <!-- 候选集1 -->
  <rect x="360" y="200" width="280" height="60" rx="8" class="stage1"/>
  <text x="500" y="220" class="node-text">📋 阶段1候选集</text>
  <text x="500" y="235" class="small-text">🗺️ 区域代表节点 | 🎯 空间分散 | 💎 高质量保证</text>
  
  <!-- 阶段2区域 -->
  <rect x="340" y="300" width="320" height="200" rx="10" class="group-bg"/>
  <text x="500" y="320" class="subtitle">阶段2: 桥节点补充</text>
  
  <!-- 桥节点过滤 -->
  <rect x="360" y="340" width="130" height="70" rx="8" class="stage2"/>
  <text x="425" y="355" class="node-text">🔍 桥节点过滤</text>
  <text x="425" y="370" class="small-text">❌ 去重处理</text>
  <text x="425" y="385" class="small-text">🔧 候选净化</text>
  
  <!-- 重要性评估 -->
  <rect x="510" y="340" width="130" height="70" rx="8" class="stage2"/>
  <text x="575" y="355" class="node-text">📊 重要性评估</text>
  <text x="575" y="370" class="small-text">🌉 连通价值</text>
  <text x="575" y="385" class="small-text">⚡ 网络影响</text>
  
  <!-- 候选集2 -->
  <rect x="360" y="420" width="280" height="60" rx="8" class="stage2"/>
  <text x="500" y="440" class="node-text">📋 阶段2候选集</text>
  <text x="500" y="455" class="small-text">🔗 桥节点集合 | 🌉 连通保证 | ⚡ 网络稳定</text>
  
  <!-- 阶段3区域 -->
  <rect x="340" y="520" width="320" height="200" rx="10" class="group-bg"/>
  <text x="500" y="540" class="subtitle">阶段3: 外围节点补充</text>
  
  <!-- 外围识别 -->
  <rect x="360" y="560" width="130" height="70" rx="8" class="stage3"/>
  <text x="425" y="575" class="node-text">🏝️ 外围识别</text>
  <text x="425" y="590" class="small-text">❌ 非区域节点</text>
  <text x="425" y="605" class="small-text">🔍 孤立检测</text>
  
  <!-- 潜力评估 -->
  <rect x="510" y="560" width="130" height="70" rx="8" class="stage3"/>
  <text x="575" y="575" class="node-text">📊 潜力评估</text>
  <text x="575" y="590" class="small-text">🎯 隐藏价值</text>
  <text x="575" y="605" class="small-text">🌟 机会挖掘</text>
  
  <!-- 候选集3 -->
  <rect x="360" y="640" width="280" height="60" rx="8" class="stage3"/>
  <text x="500" y="660" class="node-text">📋 阶段3候选集</text>
  <text x="500" y="675" class="small-text">🏝️ 外围节点 | 🔄 完整覆盖 | 🌟 边缘价值</text>
  
  <!-- 融合输出区域 -->
  <rect x="700" y="80" width="280" height="640" rx="10" class="group-bg"/>
  <text x="840" y="100" class="subtitle">多元素融合输出</text>
  
  <!-- 三集合融合 -->
  <rect x="720" y="120" width="240" height="80" rx="10" class="output"/>
  <text x="840" y="140" class="node-text">🔄 三集合融合</text>
  <text x="840" y="155" class="small-text">➕ 集合合并</text>
  <text x="840" y="170" class="small-text">📊 去重处理</text>
  <text x="840" y="185" class="small-text">✨ 统一输出</text>
  
  <!-- 质量验证 -->
  <rect x="720" y="220" width="240" height="80" rx="10" class="output"/>
  <text x="840" y="240" class="node-text">⚖️ 质量验证</text>
  <text x="840" y="255" class="small-text">📈 性能评估</text>
  <text x="840" y="270" class="small-text">🎯 目标检查</text>
  <text x="840" y="285" class="small-text">🏆 质量保证</text>
  
  <!-- 多样性验证 -->
  <rect x="720" y="320" width="240" height="80" rx="10" class="output"/>
  <text x="840" y="340" class="node-text">🎨 多样性验证</text>
  <text x="840" y="355" class="small-text">🌈 分布检查</text>
  <text x="840" y="370" class="small-text">📍 空间覆盖</text>
  <text x="840" y="385" class="small-text">🔍 结构分析</text>
  
  <!-- 最终补充集 -->
  <rect x="720" y="420" width="240" height="100" rx="10" class="output"/>
  <text x="840" y="445" class="node-text">🚀 最终补充集</text>
  <text x="840" y="460" class="small-text">💎 优化种子集合</text>
  <text x="840" y="475" class="small-text">📊 N个补充节点</text>
  <text x="840" y="490" class="small-text">🎯 算法性能增强</text>
  <text x="840" y="505" class="small-text">🌟 全面网络覆盖</text>
  
  <!-- 特征说明区域 -->
  <rect x="1020" y="80" width="560" height="640" rx="10" class="group-bg"/>
  <text x="1300" y="100" class="subtitle">多元素特征说明</text>
  
  <!-- 阶段特征对比 -->
  <rect x="1040" y="120" width="520" height="150" rx="8" fill="#fff" stroke="#999" stroke-width="1"/>
  <text x="1300" y="140" class="node-text">🎯 三阶段特征对比</text>
  
  <text x="1060" y="160" class="small-text">🟠 阶段1 - 区域均衡：</text>
  <text x="1060" y="175" class="small-text">   • 基于网络直径路径的空间划分</text>
  <text x="1060" y="190" class="small-text">   • 保证种子集的空间分散性</text>
  
  <text x="1060" y="210" class="small-text">🔴 阶段2 - 桥节点补充：</text>
  <text x="1060" y="225" class="small-text">   • 基于介数中心性的关键节点</text>
  <text x="1060" y="240" class="small-text">   • 保证网络连通性和稳定性</text>
  
  <text x="1060" y="255" class="small-text">🟣 阶段3 - 外围节点补充：</text>
  <text x="1060" y="270" class="small-text">   • 孤立连通分量中的节点</text>
  
  <!-- 技术创新点 -->
  <rect x="1040" y="290" width="520" height="120" rx="8" fill="#fff" stroke="#999" stroke-width="1"/>
  <text x="1300" y="310" class="node-text">💡 技术创新点</text>
  
  <text x="1060" y="330" class="small-text">🔧 自适应补充策略：</text>
  <text x="1060" y="345" class="small-text">   • 三阶段严格优先级机制</text>
  <text x="1060" y="360" class="small-text">   • 动态候选集过滤和去重</text>
  
  <text x="1060" y="380" class="small-text">🌟 全面网络覆盖：</text>
  <text x="1060" y="395" class="small-text">   • 主要结构 + 关键连接 + 边缘价值</text>
  
  <!-- 参数配置 -->
  <rect x="1040" y="430" width="520" height="100" rx="8" fill="#fff" stroke="#999" stroke-width="1"/>
  <text x="1300" y="450" class="node-text">⚙️ 关键参数配置</text>
  
  <text x="1060" y="470" class="small-text">• 桥节点比例：Top 10%</text>
  <text x="1060" y="485" class="small-text">• 综合评分权重：0.6×介数 + 0.4×度</text>
  <text x="1060" y="500" class="small-text">• 外围节点定义：非区域归属节点</text>
  <text x="1060" y="515" class="small-text">• 补充策略：严格优先级轮询</text>
  
  <!-- 性能优势 -->
  <rect x="1040" y="550" width="520" height="100" rx="8" fill="#fff" stroke="#999" stroke-width="1"/>
  <text x="1300" y="570" class="node-text">🚀 性能优势</text>
  
  <text x="1060" y="590" class="small-text">✅ 空间分布均衡：避免种子集聚集</text>
  <text x="1060" y="605" class="small-text">✅ 网络结构完整：保证连通性覆盖</text>
  <text x="1060" y="620" class="small-text">✅ 边缘价值挖掘：发现隐藏机会</text>
  <text x="1060" y="635" class="small-text">✅ 质量多样性平衡：最优性能保证</text>
  
  <!-- 连接线 -->
  <!-- 输入到阶段1 -->
  <line x1="300" y1="160" x2="360" y2="155" stroke="#f57c00" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="360" x2="360" y2="155" stroke="#f57c00" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="560" x2="360" y2="155" stroke="#f57c00" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 输入到阶段2 -->
  <line x1="300" y1="260" x2="360" y2="375" stroke="#c2185b" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="460" x2="360" y2="375" stroke="#c2185b" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 输入到阶段3 -->
  <line x1="300" y1="160" x2="360" y2="595" stroke="#7b1fa2" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="360" x2="360" y2="595" stroke="#7b1fa2" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 阶段内部连接 -->
  <line x1="490" y1="155" x2="510" y2="155" stroke="#f57c00" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="500" y1="190" x2="500" y2="200" stroke="#f57c00" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="490" y1="375" x2="510" y2="375" stroke="#c2185b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="500" y1="410" x2="500" y2="420" stroke="#c2185b" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="490" y1="595" x2="510" y2="595" stroke="#7b1fa2" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="500" y1="630" x2="500" y2="640" stroke="#7b1fa2" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 阶段到输出 -->
  <line x1="640" y1="230" x2="720" y2="160" stroke="#2e7d32" stroke-width="4" marker-end="url(#arrowhead)"/>
  <line x1="640" y1="450" x2="720" y2="160" stroke="#2e7d32" stroke-width="4" marker-end="url(#arrowhead)"/>
  <line x1="640" y1="670" x2="720" y2="160" stroke="#2e7d32" stroke-width="4" marker-end="url(#arrowhead)"/>
  
  <!-- 输出内部连接 -->
  <line x1="840" y1="200" x2="840" y2="220" stroke="#2e7d32" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="840" y1="300" x2="840" y2="320" stroke="#2e7d32" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="840" y1="400" x2="840" y2="420" stroke="#2e7d32" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 流程标注 -->
  <text x="320" y="75" class="small-text">多源输入</text>
  <text x="500" y="75" class="small-text">并行处理</text>
  <text x="840" y="75" class="small-text">融合输出</text>
  <text x="1300" y="75" class="small-text">特征说明</text>
</svg>
