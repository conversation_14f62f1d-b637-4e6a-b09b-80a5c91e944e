<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .node-text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
      .start-end { fill: #e1f5fe; stroke: #01579b; stroke-width: 3; }
      .process { fill: #f3e5f5; stroke: #4a148c; stroke-width: 2; }
      .decision { fill: #fff3e0; stroke: #e65100; stroke-width: 2; }
      .quality { fill: #e8f5e8; stroke: #2e7d32; stroke-width: 3; }
      .diversity { fill: #fce4ec; stroke: #c2185b; stroke-width: 3; }
      .hybrid { fill: #fff8e1; stroke: #f57f17; stroke-width: 3; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" class="title">ANFDE-IM算法总体框架图</text>
  
  <!-- 开始节点 -->
  <rect x="50" y="70" width="100" height="50" rx="25" class="start-end"/>
  <text x="100" y="100" class="node-text">🚀 开始</text>
  
  <!-- 网络预处理 -->
  <rect x="200" y="70" width="120" height="50" rx="10" class="process"/>
  <text x="260" y="90" class="node-text">🌐 网络预处理</text>
  <text x="260" y="105" class="small-text">图结构分析</text>
  
  <!-- 桥节点检测 -->
  <rect x="50" y="170" width="140" height="70" rx="10" class="process"/>
  <text x="120" y="190" class="node-text">🔗 桥节点检测</text>
  <text x="120" y="205" class="small-text">基于介数中心性</text>
  <text x="120" y="220" class="small-text">📊 Top 10%节点</text>
  
  <!-- 综合中心性计算 -->
  <rect x="230" y="170" width="140" height="70" rx="10" class="process"/>
  <text x="300" y="190" class="node-text">⚖️ 综合中心性计算</text>
  <text x="300" y="205" class="small-text">介数+度中心性</text>
  <text x="300" y="220" class="small-text">📈 MinMax归一化</text>
  
  <!-- 区域划分 -->
  <rect x="410" y="170" width="140" height="70" rx="10" class="process"/>
  <text x="480" y="190" class="node-text">🗺️ 区域划分</text>
  <text x="480" y="205" class="small-text">基于网络直径</text>
  <text x="480" y="220" class="small-text">📍 距离分层策略</text>
  
  <!-- LHS采样 -->
  <rect x="100" y="290" width="140" height="70" rx="10" class="diversity"/>
  <text x="170" y="310" class="node-text">📐 LHS采样</text>
  <text x="170" y="325" class="small-text">拉丁超立方采样</text>
  <text x="170" y="340" class="small-text">🎯 空间均匀分布</text>
  
  <!-- 启发式采样 -->
  <rect x="280" y="290" width="140" height="70" rx="10" class="quality"/>
  <text x="350" y="310" class="node-text">🧠 启发式采样</text>
  <text x="350" y="325" class="small-text">基于度中心性</text>
  <text x="350" y="340" class="small-text">⭐ 高质量种子</text>
  
  <!-- 质量筛选 -->
  <ellipse cx="300" cy="430" rx="80" ry="40" class="decision"/>
  <text x="300" y="425" class="node-text">🏆 质量筛选</text>
  <text x="300" y="440" class="small-text">第一阶段</text>
  
  <!-- 多样性筛选 -->
  <ellipse cx="300" cy="530" rx="80" ry="40" class="decision"/>
  <text x="300" y="525" class="node-text">🎨 多样性筛选</text>
  <text x="300" y="540" class="small-text">第二阶段</text>
  
  <!-- 最终初始种群 -->
  <rect x="200" y="600" width="200" height="70" rx="10" class="hybrid"/>
  <text x="300" y="620" class="node-text">✨ 最终初始种群</text>
  <text x="300" y="635" class="small-text">质量+多样性平衡</text>
  <text x="300" y="650" class="small-text">🎯 N个个体</text>
  
  <!-- 景观感知初始化 -->
  <rect x="450" y="600" width="160" height="70" rx="10" class="process"/>
  <text x="530" y="620" class="node-text">🌄 景观感知初始化</text>
  <text x="530" y="635" class="small-text">计算λ值</text>
  <text x="530" y="650" class="small-text">📈 状态判定</text>
  
  <!-- 自适应进化主循环 -->
  <rect x="650" y="600" width="180" height="70" rx="10" class="process"/>
  <text x="740" y="620" class="node-text">🔄 自适应进化主循环</text>
  <text x="740" y="635" class="small-text">多策略变异</text>
  <text x="740" y="650" class="small-text">⚙️ 参数自适应</text>
  
  <!-- 收敛判断 -->
  <ellipse cx="740" cy="500" rx="70" ry="35" class="decision"/>
  <text x="740" y="505" class="node-text">🔍 收敛判断</text>
  
  <!-- 多样性维护 -->
  <rect x="850" y="400" width="160" height="70" rx="10" class="process"/>
  <text x="930" y="420" class="node-text">🔧 多样性维护</text>
  <text x="930" y="435" class="small-text">桥节点注入</text>
  <text x="930" y="450" class="small-text">🌊 社区感知扰动</text>
  
  <!-- 输出最优解 -->
  <rect x="650" y="350" width="180" height="50" rx="25" class="start-end"/>
  <text x="740" y="375" class="node-text">🏁 输出最优解</text>
  <text x="740" y="390" class="small-text">最佳种子集</text>
  
  <!-- 连接线 -->
  <line x1="150" y1="95" x2="200" y2="95" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="260" y1="120" x2="120" y2="170" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="260" y1="120" x2="300" y2="170" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="260" y1="120" x2="480" y2="170" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="120" y1="240" x2="170" y2="290" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="240" x2="170" y2="290" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="480" y1="240" x2="170" y2="290" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="300" y1="240" x2="350" y2="290" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="120" y1="240" x2="350" y2="290" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="170" y1="360" x2="250" y2="400" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="350" y1="360" x2="350" y2="400" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="300" y1="470" x2="300" y2="490" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="570" x2="300" y2="600" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="400" y1="635" x2="450" y2="635" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="610" y1="635" x2="650" y2="635" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="740" y1="600" x2="740" y2="535" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="810" y1="500" x2="930" y2="470" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="740" y1="465" x2="740" y2="400" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="850" y1="435" x2="800" y2="500" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 标注 -->
  <text x="820" y="485" class="small-text">否</text>
  <text x="740" y="445" class="small-text">是</text>
</svg>
