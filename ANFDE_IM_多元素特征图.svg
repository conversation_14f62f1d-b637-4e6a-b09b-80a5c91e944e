<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; text-anchor: middle; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .node-text { font-family: Arial, sans-serif; font-size: 11px; text-anchor: middle; }
      .small-text { font-family: Arial, sans-serif; font-size: 9px; text-anchor: middle; }
      .element1 { fill: #e3f2fd; stroke: #1976d2; stroke-width: 3; }
      .element2 { fill: #e8f5e8; stroke: #388e3c; stroke-width: 3; }
      .element3 { fill: #fce4ec; stroke: #c2185b; stroke-width: 3; }
      .element4 { fill: #fff3e0; stroke: #f57c00; stroke-width: 3; }
      .fusion { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 4; }
      .center { fill: #fff8e1; stroke: #f57f17; stroke-width: 5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" class="title">ANFDE-IM多元素初始化框架</text>
  <text x="600" y="50" class="subtitle">质量筛选 × 多样性筛选 = 平衡种群</text>
  
  <!-- 中心融合节点 -->
  <circle cx="600" cy="300" r="80" class="center"/>
  <text x="600" y="285" class="node-text">🎯 混合初始化</text>
  <text x="600" y="300" class="small-text">质量+多样性</text>
  <text x="600" y="315" class="small-text">平衡融合</text>
  <text x="600" y="330" class="small-text">N个个体</text>
  
  <!-- 元素1：LHS采样 -->
  <rect x="100" y="100" width="180" height="100" rx="15" class="element1"/>
  <text x="190" y="125" class="node-text">📐 LHS采样</text>
  <text x="190" y="140" class="small-text">🌐 拉丁超立方</text>
  <text x="190" y="155" class="small-text">🎯 空间均匀分布</text>
  <text x="190" y="170" class="small-text">💎 多样性保证</text>
  <text x="190" y="185" class="small-text">📊 SN/2个解</text>
  
  <!-- 元素2：启发式采样 -->
  <rect x="920" y="100" width="180" height="100" rx="15" class="element2"/>
  <text x="1010" y="125" class="node-text">🧠 启发式采样</text>
  <text x="1010" y="140" class="small-text">⭐ 度中心性导向</text>
  <text x="1010" y="155" class="small-text">🎯 质量优先选择</text>
  <text x="1010" y="170" class="small-text">⚡ 性能保证</text>
  <text x="1010" y="185" class="small-text">📊 SN/2个解</text>
  
  <!-- 元素3：质量筛选 -->
  <rect x="100" y="400" width="180" height="100" rx="15" class="element3"/>
  <text x="190" y="425" class="node-text">🏆 质量筛选</text>
  <text x="190" y="440" class="small-text">📊 LIE影响力评估</text>
  <text x="190" y="455" class="small-text">⚡ 并行计算优化</text>
  <text x="190" y="470" class="small-text">🥇 前70%选择</text>
  <text x="190" y="485" class="small-text">💾 缓存加速</text>
  
  <!-- 元素4：多样性筛选 -->
  <rect x="920" y="400" width="180" height="100" rx="15" class="element4"/>
  <text x="1010" y="425" class="node-text">🌈 多样性筛选</text>
  <text x="1010" y="440" class="small-text">🎨 Jaccard相似度</text>
  <text x="1010" y="455" class="small-text">✂️ 相似解剔除</text>
  <text x="1010" y="470" class="small-text">🌟 空间分布优化</text>
  <text x="1010" y="485" class="small-text">🔧 动态阈值调整</text>
  
  <!-- 辅助元素：桥节点检测 -->
  <ellipse cx="350" cy="200" rx="80" ry="40" class="fusion"/>
  <text x="350" y="195" class="node-text">🔗 桥节点检测</text>
  <text x="350" y="210" class="small-text">📊 介数中心性 Top 10%</text>
  
  <!-- 辅助元素：综合中心性 -->
  <ellipse cx="850" cy="200" rx="80" ry="40" class="fusion"/>
  <text x="850" y="195" class="node-text">⚖️ 综合中心性</text>
  <text x="850" y="210" class="small-text">0.6×介数 + 0.4×度</text>
  
  <!-- 辅助元素：区域划分 -->
  <ellipse cx="350" cy="400" rx="80" ry="40" class="fusion"/>
  <text x="350" y="395" class="node-text">🗺️ 区域划分</text>
  <text x="350" y="410" class="small-text">基于网络直径分层</text>
  
  <!-- 辅助元素：加权PDI距离 -->
  <ellipse cx="850" cy="400" rx="80" ry="40" class="fusion"/>
  <text x="850" y="395" class="node-text">📏 加权PDI距离</text>
  <text x="850" y="410" class="small-text">基于LFV值加权计算</text>
  
  <!-- 连接线 -->
  <!-- 主要元素到中心 -->
  <line x1="280" y1="150" x2="540" y2="250" stroke="#1976d2" stroke-width="4" marker-end="url(#arrowhead)"/>
  <line x1="920" y1="150" x2="660" y2="250" stroke="#388e3c" stroke-width="4" marker-end="url(#arrowhead)"/>
  <line x1="280" y1="450" x2="540" y2="350" stroke="#c2185b" stroke-width="4" marker-end="url(#arrowhead)"/>
  <line x1="920" y1="450" x2="660" y2="350" stroke="#f57c00" stroke-width="4" marker-end="url(#arrowhead)"/>
  
  <!-- 辅助元素到中心 -->
  <line x1="430" y1="200" x2="520" y2="280" stroke="#7b1fa2" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="770" y1="200" x2="680" y2="280" stroke="#7b1fa2" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="430" y1="400" x2="520" y2="320" stroke="#7b1fa2" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="770" y1="400" x2="680" y2="320" stroke="#7b1fa2" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 元素间协作连接 -->
  <line x1="280" y1="150" x2="350" y2="160" stroke="#999" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="920" y1="150" x2="850" y2="160" stroke="#999" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="280" y1="450" x2="350" y2="440" stroke="#999" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="920" y1="450" x2="850" y2="440" stroke="#999" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- 特征标签 -->
  <text x="190" y="80" class="subtitle" fill="#1976d2">多样性导向</text>
  <text x="1010" y="80" class="subtitle" fill="#388e3c">质量导向</text>
  <text x="190" y="530" class="subtitle" fill="#c2185b">性能筛选</text>
  <text x="1010" y="530" class="subtitle" fill="#f57c00">结构筛选</text>
  
  <!-- 流程说明 -->
  <rect x="450" y="480" width="300" height="80" rx="10" fill="#f5f5f5" stroke="#999" stroke-width="1"/>
  <text x="600" y="500" class="node-text">🔄 多元素融合流程</text>
  <text x="600" y="515" class="small-text">1. 并行多源采样生成</text>
  <text x="600" y="530" class="small-text">2. 质量与多样性双重筛选</text>
  <text x="600" y="545" class="small-text">3. 智能平衡融合输出</text>
  
  <!-- 创新点标注 -->
  <circle cx="100" cy="50" r="8" fill="#ff5722"/>
  <text x="120" y="55" class="small-text" fill="#ff5722">创新：加权PDI距离</text>
  
  <circle cx="300" cy="50" r="8" fill="#ff5722"/>
  <text x="320" y="55" class="small-text" fill="#ff5722">创新：四状态景观感知</text>
  
  <circle cx="550" cy="50" r="8" fill="#ff5722"/>
  <text x="570" y="55" class="small-text" fill="#ff5722">创新：自适应桥节点检测</text>
</svg>
