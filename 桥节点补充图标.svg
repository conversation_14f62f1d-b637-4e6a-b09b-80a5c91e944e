<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .cluster { fill: #fce4ec; stroke: #f48fb1; stroke-width: 1.5; opacity: 0.4; }
      .cluster-border { fill: none; stroke: #f48fb1; stroke-width: 1.5; stroke-dasharray: 3,3; }
      .bridge-node { fill: #f06292; stroke: #e91e63; stroke-width: 2; }
      .normal-node { fill: #f8bbd9; stroke: #f48fb1; stroke-width: 1; }
      .bridge-connection { stroke: #f06292; stroke-width: 2.5; opacity: 0.8; }
      .normal-connection { stroke: #f8bbd9; stroke-width: 1.5; opacity: 0.7; }
      .bridge-icon { fill: #e91e63; stroke: #c2185b; stroke-width: 0.8; }
      .text { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; text-anchor: middle; fill: #c2185b; }
      .small-text { font-family: Arial, sans-serif; font-size: 8px; text-anchor: middle; fill: #f48fb1; }
    </style>
    <filter id="bridge-glow">
      <feGaussianBlur stdDeviation="2.5" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 左侧集群 -->
  <ellipse cx="60" cy="80" rx="35" ry="45" class="cluster"/>
  <ellipse cx="60" cy="80" rx="35" ry="45" class="cluster-border"/>
  
  <!-- 右侧集群 -->
  <ellipse cx="140" cy="120" rx="35" ry="40" class="cluster"/>
  <ellipse cx="140" cy="120" rx="35" ry="40" class="cluster-border"/>
  
  <!-- 下方集群 -->
  <ellipse cx="100" cy="160" rx="30" ry="25" class="cluster"/>
  <ellipse cx="100" cy="160" rx="30" ry="25" class="cluster-border"/>
  
  <!-- 左侧集群内的节点 -->
  <circle cx="45" cy="65" r="5" class="normal-node"/>
  <circle cx="60" cy="70" r="5" class="normal-node"/>
  <circle cx="75" cy="80" r="5" class="normal-node"/>
  <circle cx="50" cy="95" r="5" class="normal-node"/>
  <circle cx="65" cy="100" r="5" class="normal-node"/>
  
  <!-- 右侧集群内的节点 -->
  <circle cx="125" cy="105" r="5" class="normal-node"/>
  <circle cx="140" cy="110" r="5" class="normal-node"/>
  <circle cx="155" cy="120" r="5" class="normal-node"/>
  <circle cx="130" cy="135" r="5" class="normal-node"/>
  <circle cx="150" cy="140" r="5" class="normal-node"/>
  
  <!-- 下方集群内的节点 -->
  <circle cx="85" cy="155" r="4" class="normal-node"/>
  <circle cx="100" cy="160" r="4" class="normal-node"/>
  <circle cx="115" cy="165" r="4" class="normal-node"/>
  
  <!-- 桥节点 -->
  <circle cx="90" cy="100" r="8" class="bridge-node" filter="url(#bridge-glow)"/>
  <circle cx="120" cy="140" r="7" class="bridge-node" filter="url(#bridge-glow)"/>
  
  <!-- 集群内连接 -->
  <line x1="45" y1="65" x2="60" y2="70" class="normal-connection"/>
  <line x1="60" y1="70" x2="75" y2="80" class="normal-connection"/>
  <line x1="50" y1="95" x2="65" y2="100" class="normal-connection"/>
  <line x1="75" y1="80" x2="65" y2="100" class="normal-connection"/>
  
  <line x1="125" y1="105" x2="140" y2="110" class="normal-connection"/>
  <line x1="140" y1="110" x2="155" y2="120" class="normal-connection"/>
  <line x1="130" y1="135" x2="150" y2="140" class="normal-connection"/>
  <line x1="155" y1="120" x2="150" y2="140" class="normal-connection"/>
  
  <line x1="85" y1="155" x2="100" y2="160" class="normal-connection"/>
  <line x1="100" y1="160" x2="115" y2="165" class="normal-connection"/>
  
  <!-- 桥连接（关键连接） -->
  <line x1="75" y1="80" x2="90" y2="100" class="bridge-connection"/>
  <line x1="90" y1="100" x2="125" y2="105" class="bridge-connection"/>
  <line x1="120" y1="140" x2="130" y2="135" class="bridge-connection"/>
  <line x1="120" y1="140" x2="100" y2="160" class="bridge-connection"/>
  <line x1="90" y1="100" x2="120" y2="140" class="bridge-connection"/>
  
  <!-- 桥梁图标 -->
  <g transform="translate(90,100)">
    <!-- 桥梁主体 -->
    <rect x="-10" y="-2" width="20" height="4" rx="2" class="bridge-icon"/>
    <!-- 桥墩 -->
    <rect x="-1.5" y="-6" width="3" height="12" class="bridge-icon"/>
    <rect x="-8" y="-4" width="2.5" height="8" class="bridge-icon"/>
    <rect x="5.5" y="-4" width="2.5" height="8" class="bridge-icon"/>
    <!-- 桥拱 -->
    <path d="M -8,-2 Q 0,-4 8,-2" fill="none" stroke="#e91e63" stroke-width="1"/>
  </g>

  <g transform="translate(120,140)">
    <!-- 小桥梁 -->
    <rect x="-8" y="-1.5" width="16" height="3" rx="1.5" class="bridge-icon"/>
    <rect x="-1" y="-4" width="2" height="8" class="bridge-icon"/>
    <rect x="-6" y="-3" width="1.5" height="6" class="bridge-icon"/>
    <rect x="4.5" y="-3" width="1.5" height="6" class="bridge-icon"/>
    <!-- 小桥拱 -->
    <path d="M -6,-1.5 Q 0,-3 6,-1.5" fill="none" stroke="#e91e63" stroke-width="0.8"/>
  </g>
  
  <!-- 箭头指向桥节点 -->
  <defs>
    <marker id="bridge-arrow" markerWidth="6" markerHeight="4" refX="5" refY="2" orient="auto">
      <polygon points="0 0, 6 2, 0 4" fill="#c2185b"/>
    </marker>
  </defs>

  <path d="M 35 55 Q 60 75 82 92" fill="none" stroke="#c2185b" stroke-width="1.5" marker-end="url(#bridge-arrow)"/>
  <path d="M 165 165 Q 145 155 128 148" fill="none" stroke="#c2185b" stroke-width="1.5" marker-end="url(#bridge-arrow)"/>
  
  <!-- 标题和说明 -->
  <text x="100" y="25" class="text">桥节点补充</text>
  <text x="100" y="195" class="small-text">连接不同集群的关键节点</text>
  
  <!-- 图例 -->
  <g transform="translate(15,15)">
    <circle cx="0" cy="0" r="4" class="bridge-node"/>
    <text x="10" y="3" class="small-text">桥节点</text>
  </g>
  
  <g transform="translate(15,30)">
    <line x1="0" y1="0" x2="15" y2="0" class="bridge-connection"/>
    <text x="20" y="3" class="small-text">桥连接</text>
  </g>
  
  <g transform="translate(120,15)">
    <ellipse cx="0" cy="0" rx="12" ry="8" class="cluster-border"/>
    <text x="18" y="3" class="small-text">节点集群</text>
  </g>
</svg>
