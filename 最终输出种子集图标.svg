<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .treasure-chest { fill: #8d6e63; stroke: #5d4037; stroke-width: 2; }
      .chest-highlight { fill: #a1887f; stroke: #6d4c41; stroke-width: 1; }
      .gold-coin { fill: #ffd700; stroke: #ffb300; stroke-width: 1.5; }
      .seed-node { fill: #4caf50; stroke: #2e7d32; stroke-width: 2; }
      .premium-seed { fill: #66bb6a; stroke: #388e3c; stroke-width: 2; }
      .connection-line { stroke: #81c784; stroke-width: 1.5; opacity: 0.7; }
      .sparkle { fill: #fff176; stroke: #f57f17; stroke-width: 0.5; }
      .output-glow { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; opacity: 0.6; }
      .text { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
      .small-text { font-family: Arial, sans-serif; font-size: 8px; text-anchor: middle; fill: #4caf50; }
      .result-text { font-family: Arial, sans-serif; font-size: 10px; font-weight: bold; text-anchor: middle; fill: #1b5e20; }
    </style>
    <filter id="treasure-glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <filter id="seed-glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景光环 -->
  <circle cx="100" cy="100" r="85" class="output-glow" filter="url(#treasure-glow)"/>
  
  <!-- 宝箱主体 -->
  <rect x="60" y="120" width="80" height="50" rx="8" class="treasure-chest"/>
  <!-- 宝箱盖子 -->
  <ellipse cx="100" cy="120" rx="40" ry="15" class="treasure-chest"/>
  <!-- 宝箱装饰 -->
  <rect x="65" y="130" width="70" height="3" class="chest-highlight"/>
  <rect x="65" y="150" width="70" height="3" class="chest-highlight"/>
  <!-- 宝箱锁扣 -->
  <rect x="95" y="140" width="10" height="15" rx="2" class="chest-highlight"/>
  <circle cx="100" cy="147" r="3" fill="#ffd700"/>
  
  <!-- 从宝箱中飞出的种子节点 -->
  <!-- 第一层种子 -->
  <circle cx="70" cy="80" r="8" class="seed-node" filter="url(#seed-glow)"/>
  <circle cx="100" cy="70" r="9" class="premium-seed" filter="url(#seed-glow)"/>
  <circle cx="130" cy="80" r="8" class="seed-node" filter="url(#seed-glow)"/>
  
  <!-- 第二层种子 -->
  <circle cx="50" cy="100" r="7" class="seed-node" filter="url(#seed-glow)"/>
  <circle cx="85" cy="90" r="6" class="seed-node"/>
  <circle cx="115" cy="90" r="6" class="seed-node"/>
  <circle cx="150" cy="100" r="7" class="seed-node" filter="url(#seed-glow)"/>
  
  <!-- 第三层种子 -->
  <circle cx="40" cy="120" r="5" class="seed-node"/>
  <circle cx="160" cy="120" r="5" class="seed-node"/>
  
  <!-- 种子间的连接线 -->
  <line x1="70" y1="80" x2="85" y2="90" class="connection-line"/>
  <line x1="100" y1="70" x2="85" y2="90" class="connection-line"/>
  <line x1="100" y1="70" x2="115" y2="90" class="connection-line"/>
  <line x1="130" y1="80" x2="115" y2="90" class="connection-line"/>
  <line x1="50" y1="100" x2="70" y2="80" class="connection-line"/>
  <line x1="150" y1="100" x2="130" y2="80" class="connection-line"/>
  <line x1="40" y1="120" x2="50" y2="100" class="connection-line"/>
  <line x1="160" y1="120" x2="150" y2="100" class="connection-line"/>
  
  <!-- 金币表示价值 -->
  <circle cx="75" cy="110" r="4" class="gold-coin"/>
  <text x="75" y="113" style="font-size:6px; text-anchor:middle; fill:#ff8f00;">$</text>
  
  <circle cx="125" cy="110" r="4" class="gold-coin"/>
  <text x="125" y="113" style="font-size:6px; text-anchor:middle; fill:#ff8f00;">$</text>
  
  <!-- 闪烁效果 -->
  <g class="sparkle">
    <polygon points="45,70 47,75 52,75 48,78 50,83 45,80 40,83 42,78 38,75 43,75"/>
    <polygon points="155,70 157,75 162,75 158,78 160,83 155,80 150,83 152,78 148,75 153,75"/>
    <polygon points="100,50 102,55 107,55 103,58 105,63 100,60 95,63 97,58 93,55 98,55"/>
    <polygon points="30,110 32,115 37,115 33,118 35,123 30,120 25,123 27,118 23,115 28,115"/>
    <polygon points="170,110 172,115 177,115 173,118 175,123 170,120 165,123 167,118 163,115 168,115"/>
  </g>
  
  <!-- 种子节点标识 -->
  <g transform="translate(70,80)">
    <text x="0" y="-12" style="font-size:6px; text-anchor:middle; fill:#1b5e20;">S1</text>
  </g>
  <g transform="translate(100,70)">
    <text x="0" y="-15" style="font-size:7px; text-anchor:middle; fill:#1b5e20; font-weight:bold;">S*</text>
  </g>
  <g transform="translate(130,80)">
    <text x="0" y="-12" style="font-size:6px; text-anchor:middle; fill:#1b5e20;">S2</text>
  </g>
  <g transform="translate(50,100)">
    <text x="0" y="-10" style="font-size:5px; text-anchor:middle; fill:#1b5e20;">S3</text>
  </g>
  <g transform="translate(150,100)">
    <text x="0" y="-10" style="font-size:5px; text-anchor:middle; fill:#1b5e20;">S4</text>
  </g>
  
  <!-- 输出箭头 -->
  <defs>
    <marker id="output-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#2e7d32"/>
    </marker>
  </defs>
  
  <!-- 从算法到宝箱的箭头 -->
  <path d="M 100 30 Q 100 40 100 50" fill="none" stroke="#2e7d32" stroke-width="3" marker-end="url(#output-arrow)"/>
  
  <!-- 循环结束标识 -->
  <g transform="translate(100,35)">
    <circle cx="0" cy="0" r="12" fill="#fff" stroke="#2e7d32" stroke-width="2"/>
    <text x="0" y="4" style="font-size:8px; text-anchor:middle; fill:#2e7d32; font-weight:bold;">END</text>
  </g>
  
  <!-- 标题和说明 -->
  <text x="100" y="20" class="text">最终输出种子集</text>
  <text x="100" y="190" class="small-text">算法收敛，输出最优种子组合</text>
  
  <!-- 性能指标 -->
  <rect x="20" y="160" width="160" height="25" rx="5" fill="#f1f8e9" stroke="#8bc34a" stroke-width="1"/>
  <text x="100" y="172" class="result-text">影响力覆盖: 95.2% | 种子数量: 5个</text>
  
  <!-- 图例 -->
  <g transform="translate(15,15)">
    <circle cx="0" cy="0" r="4" class="premium-seed"/>
    <text x="10" y="3" class="small-text">最优种子</text>
  </g>
  
  <g transform="translate(80,15)">
    <circle cx="0" cy="0" r="3" class="seed-node"/>
    <text x="8" y="3" class="small-text">种子节点</text>
  </g>
  
  <g transform="translate(140,15)">
    <circle cx="0" cy="0" r="3" class="gold-coin"/>
    <text x="8" y="3" class="small-text">价值评估</text>
  </g>
  
  <!-- 成功标识 -->
  <g transform="translate(170,40)">
    <circle cx="0" cy="0" r="8" fill="#4caf50"/>
    <path d="M -3,-1 L -1,2 L 4,-3" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round"/>
  </g>
</svg>
