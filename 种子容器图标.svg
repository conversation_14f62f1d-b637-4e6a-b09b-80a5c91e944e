<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="70" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .container { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .container-lid { fill: #c8e6c9; stroke: #4caf50; stroke-width: 1.5; }
      .seed-cube { fill: #66bb6a; stroke: #2e7d32; stroke-width: 1; }
      .best-cube { fill: #4caf50; stroke: #1b5e20; stroke-width: 1.5; }
      .label { fill: #4caf50; stroke: #2e7d32; stroke-width: 1; }
      .text { font-family: Arial, sans-serif; font-size: 7px; font-weight: bold; text-anchor: middle; fill: #fff; }
      .title-text { font-family: Arial, sans-serif; font-size: 8px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
      .small-text { font-family: Arial, sans-serif; font-size: 6px; text-anchor: middle; fill: #4caf50; }
      .count-text { font-family: Arial, sans-serif; font-size: 5px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
    </style>
    <filter id="container-shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- 容器主体 -->
  <rect x="20" y="30" width="60" height="30" rx="5" class="container" filter="url(#container-shadow)"/>
  
  <!-- 容器盖子 -->
  <ellipse cx="50" cy="30" rx="30" ry="8" class="container-lid"/>
  
  <!-- 标签 -->
  <rect x="25" y="20" width="50" height="8" rx="4" class="label"/>
  <text x="50" y="25" class="text">SEED COLLECTION</text>
  
  <!-- 种子块（用立方体表示，避免与圆形节点混淆） -->
  <!-- 最优种子（最大的立方体） -->
  <g transform="translate(45,40)">
    <rect x="0" y="0" width="8" height="6" rx="1" class="best-cube"/>
    <polygon points="8,0 10,2 10,8 8,6" fill="#388e3c"/>
    <polygon points="0,0 2,2 10,2 8,0" fill="#2e7d32"/>
    <text x="4" y="-2" style="font-size:4px; text-anchor:middle; fill:#1b5e20; font-weight:bold;">s*</text>
  </g>
  
  <!-- 普通种子 -->
  <g transform="translate(30,45)">
    <rect x="0" y="0" width="5" height="4" rx="0.5" class="seed-cube"/>
    <polygon points="5,0 6.5,1.5 6.5,5.5 5,4" fill="#4caf50"/>
    <polygon points="0,0 1.5,1.5 6.5,1.5 5,0" fill="#388e3c"/>
    <text x="2.5" y="-1" style="font-size:3px; text-anchor:middle; fill:#2e7d32;">s₁</text>
  </g>
  
  <g transform="translate(55,45)">
    <rect x="0" y="0" width="5" height="4" rx="0.5" class="seed-cube"/>
    <polygon points="5,0 6.5,1.5 6.5,5.5 5,4" fill="#4caf50"/>
    <polygon points="0,0 1.5,1.5 6.5,1.5 5,0" fill="#388e3c"/>
    <text x="2.5" y="-1" style="font-size:3px; text-anchor:middle; fill:#2e7d32;">s₂</text>
  </g>
  
  <g transform="translate(35,50)">
    <rect x="0" y="0" width="4" height="3" rx="0.5" class="seed-cube"/>
    <polygon points="4,0 5,1 5,4 4,3" fill="#4caf50"/>
    <polygon points="0,0 1,1 5,1 4,0" fill="#388e3c"/>
    <text x="2" y="-1" style="font-size:3px; text-anchor:middle; fill:#2e7d32;">s₃</text>
  </g>
  
  <g transform="translate(50,50)">
    <rect x="0" y="0" width="4" height="3" rx="0.5" class="seed-cube"/>
    <polygon points="4,0 5,1 5,4 4,3" fill="#4caf50"/>
    <polygon points="0,0 1,1 5,1 4,0" fill="#388e3c"/>
    <text x="2" y="-1" style="font-size:3px; text-anchor:middle; fill:#2e7d32;">s₄</text>
  </g>
  
  <!-- 数量标识 -->
  <g transform="translate(75,35)">
    <circle cx="0" cy="0" r="6" fill="#ff9800" stroke="#f57c00" stroke-width="1"/>
    <text x="0" y="-1" style="font-size:3px; text-anchor:middle; fill:#fff;">COUNT</text>
    <text x="0" y="2" style="font-size:5px; text-anchor:middle; fill:#fff; font-weight:bold;">5</text>
  </g>
  
  <!-- 质量标识 -->
  <g transform="translate(15,40)">
    <rect x="0" y="0" width="8" height="10" rx="1" fill="#4caf50"/>
    <text x="4" y="3" style="font-size:3px; text-anchor:middle; fill:#fff;">GRADE</text>
    <text x="4" y="7" style="font-size:4px; text-anchor:middle; fill:#fff; font-weight:bold;">A+</text>
  </g>
  
  <!-- 标题 -->
  <text x="50" y="12" class="title-text">📦 种子容器</text>
  <text x="50" y="67" class="small-text">优质种子节点收集容器</text>
</svg>
