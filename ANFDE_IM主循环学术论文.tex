\documentclass[12pt]{article}
\usepackage[utf8]{inputenc}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{xeCJK}
\usepackage{geometry}
\geometry{a4paper,margin=2.5cm}

\title{ANFDE-IM算法：基于地形状态感知的自适应差分进化}
\author{研究团队}
\date{\today}

\begin{document}

\maketitle

\section{地形状态感知的差分进化}

\subsection{种群初始化}

传统研究通常采用单一方法进行种群初始化，其中基于度数递减的方法最为典型。为了增强种群的多样性，本研究提出使用多种初始化方法。在地形状态特征化阶段，已通过两种采样方法获得了样本集，因此可以利用该样本集进行种群初始化。

具体的初始化机制如算法\ref{alg:initialization}所述。为避免重复选择相同元素，采用将选中元素与最后一个未选元素交换的策略。这种方法确保每个元素只被选择一次，从而保证初始化过程的随机性和效率。

\begin{algorithm}[H]
\caption{种群初始化}
\label{alg:initialization}
\begin{algorithmic}[1]
\REQUIRE 样本集$P$，种群大小$pop$
\ENSURE 初始种群$X$
\STATE 初始化$X$
\STATE $end = |P|$
\FOR{$i = 1$ to $pop$}
    \STATE $j = random(pop)$
    \STATE $X_i = P_j$, $P_i = P_{end}$
    \STATE 移除$P_{end}$
    \STATE $end = end - 1$
\ENDFOR
\RETURN $X$
\end{algorithmic}
\end{algorithm}

\subsection{变异操作}

在差分进化中，变异操作通过种群中个体间的差异生成新个体。变异操作的核心思想是利用种群中随机选择个体间的差异形成新的候选解。经典的差分变异操作公式如式(\ref{eq:classic_mutation})所示：

\begin{equation}
\label{eq:classic_mutation}
M_i = X_{r1} + F \cdot (X_{r2} - X_{r3})
\end{equation}

其中$M_i$表示生成的变异个体，$X_{r1}$、$X_{r2}$和$X_{r3}$是从种群中随机选择的三个不同个体，$F$是调节差异向量大小的缩放因子。

在原始差分进化算法中，通过差分算子计算两个向量在各维度上的差异，这些差异用于表示个体在解空间中的偏移。然而，变异操作需要特定修改才能适用于求解离散的影响力最大化问题。

具体而言，首先生成基准个体$X_{r1}$，其节点通过差异操作的节点集进行替换。生成规则如式(\ref{eq:base_generation})所示：

\begin{equation}
\label{eq:base_generation}
X_{r1} = X.get(random(pop))
\end{equation}

然后，从种群中随机选择两个不同个体作为$X_{r2}$和$X_{r3}$。生成规则如式(\ref{eq:pair_generation})所示：

\begin{equation}
\label{eq:pair_generation}
\begin{cases}
X_{r2} = X.get(random(pop)) \\
X_{r3} = X.get(random(pop)) \\
s.t. \quad X_{r2} \neq X_{r3}
\end{cases}
\end{equation}

差异操作$X_{r2} - X_{r3}$涉及从$X_{r2}$中移除$X_{r3}$中的节点，得到差异集$D$。对于个体$X_{r1}$，需要用$D$中的节点替换一定数量的节点。替换数量由缩放因子$F$和差异集$D$的大小共同决定，目标是增强个体的影响力。具体实现规则如式(\ref{eq:replacement_count})所示：

\begin{equation}
\label{eq:replacement_count}
N = F \cdot |D|
\end{equation}

其中$N$和$F$分别表示需要替换的节点数量和缩放因子，$|D|$表示差异集的大小。

在变异过程中，$M_i$中需要被替换的节点是个体中影响力最小的节点。获取节点索引$x$的规则如式(\ref{eq:node_selection})所示：

\begin{equation}
\label{eq:node_selection}
x = \arg \min_{j \in \{1,2,\ldots,k\}} \{LFV(M_{ij})\}
\end{equation}

其中$M_{ij}$表示个体$M_i$中的第$j$个节点（$j = 1, \ldots, k$），$LFV$存储所有节点$V$的局部影响力值。

差分变异过程中存在两种处理情况。首先，如果个体$M_i$与差异集$D$相同，则采用搜索策略从具有大影响力的节点集中选择唯一节点替换$M_{ix}$。其次，如果$M_i$与差异集$D$不同，算法从差异集$D$中选择随机节点替换$M_{ix}$。更新规则如式(\ref{eq:update_rules})所示：

\begin{equation}
\label{eq:update_rules}
M_{ix} = \begin{cases}
uniq(QAS(G, k)), & \text{if } M_i = D \\
D.get(random(|D|)), & \text{otherwise}
\end{cases}
\end{equation}

具体细节如算法\ref{alg:mutation}所述。

\begin{algorithm}[H]
\caption{变异操作}
\label{alg:mutation}
\begin{algorithmic}[1]
\REQUIRE 原始种群$X$，缩放因子$F$，种群大小$pop$，种子集大小$k$
\ENSURE 变异种群$M$
\STATE 初始化$M$
\FOR{$i=1$ to $pop$}
    \STATE 根据式(\ref{eq:base_generation})生成$X_{r1}$
    \STATE 根据式(\ref{eq:pair_generation})生成$X_{r2}$和$X_{r3}$
    \STATE $M_i = X_{r1}$
    \FOR{$a=1$ to $N$} // $N$表示需要替换的节点数量
        \STATE 根据式(\ref{eq:node_selection})计算被替换节点的索引$x$
        \STATE 根据式(\ref{eq:update_rules})更新$M_{ix}$
    \ENDFOR
\ENDFOR
\RETURN $M$
\end{algorithmic}
\end{algorithm}

\subsection{交叉操作}

交叉操作旨在维持当前代中原始个体和变异个体之间的多样性。在每一代中，根据交叉概率，个体中的某些元素来自变异个体，而其他元素则保留自原始个体。具体生成规则如式(\ref{eq:crossover_rules})所示：

\begin{equation}
\label{eq:crossover_rules}
C_{ij} = \begin{cases}
M_{ij}, & \text{if } random < cr \text{ and } M_{ij} \notin C_i \\
uniq(QAS(G, k)), & \text{if } random < cr \text{ and } M_{ij} \in C_i \text{ and } X_{ij} \in C_i \\
X_{ij}, & \text{if } random < cr \text{ and } M_{ij} \in C_i \text{ and } X_{ij} \notin C_i \\
X_{ij}, & \text{if } random \geq cr \text{ and } X_{ij} \notin C_i \\
uniq(QAS(G, k)), & \text{if } random \geq cr \text{ and } M_{ij} \in C_i \text{ and } X_{ij} \in C_i \\
M_{ij}, & \text{if } random \geq cr \text{ and } X_{ij} \in C_i \text{ and } M_{ij} \notin C_i
\end{cases}
\end{equation}

其中$C_i$、$C_{ij}$、$X_{ij}$、$M_{ij}$和$cr$分别表示交叉个体、交叉个体的节点、原始个体的节点、变异个体的节点和交叉概率。

\subsection{选择操作}

在选择过程中，采用贪心策略比较当前代原始种群中每个个体与其对应交叉个体的$EDV$值。选择$EDV$值较大的个体作为胜者进入下一代。具体更新规则如式(\ref{eq:selection_rules})所示：

\begin{equation}
\label{eq:selection_rules}
X_i = \arg \max\{EDV(X_i), EDV(C_i)\}
\end{equation}

其中$EDV(X_i)$和$EDV(C_i)$分别表示原始个体$X_i$和交叉个体$C_i$的适应度值。

\section{地形状态感知的进化机制}

本节提出一种融合地形状态特征的指导方法，以增强差分进化在求解影响力最大化问题时的质量和效率。该机制包含两个主要步骤：首先确定当前种群是否需要实施指导策略；然后基于地形划分结果，对处于不同地形区域的个体采用不同的优化策略。

\subsection{指标计算}

在以往研究中，研究者提出了单值度量$R'$来表征优化函数的粗糙度。受此启发，可以通过度量$R'$评估影响力最大化问题中种群地形的粗糙度。

为近似$R'$的理论值，计算集合$\{0, \frac{\epsilon^*}{128}, \frac{\epsilon^*}{64}, \frac{\epsilon^*}{32}, \frac{\epsilon^*}{16}, \frac{\epsilon^*}{8}, \frac{\epsilon^*}{4}, \frac{\epsilon^*}{2}, \epsilon^*\}$中所有$\epsilon$值的$H(\epsilon)$最大值。$R'$的公式如式(\ref{eq:ruggedness})所示：

\begin{equation}
\label{eq:ruggedness}
R' = \max_{\forall\epsilon \in [0,\epsilon^*]} \{H(\epsilon)\}
\end{equation}

其中$\epsilon^*$是阈值参数，通常取自然常数$e$，$H(\epsilon)$是通过式(5)计算的熵度量。选择$e$作为阈值与自然对数的数学性质一致，可以简化数值评估。以往关于适应度地形的研究通过使用$e$作为阈值参数取得了积极结果。

对于种群的每一代，如果地形粗糙度没有变化，表明算法的进化已经停滞。因此，通过评估$R'$值的变化，可以识别种群是否陷入局部最优，并据此决定是否实施地形指导策略。

\subsection{基于地形的指导策略}

一旦确定指导策略，下一步是划分适应度地形。通过可视化影响力最大化问题解空间的适应度地形，可以探索解的分布特征。结果表明，解空间呈现山坡结构。基于此，可以将地形划分为三个区域：山底、山脊和山顶。

通过评估个体在适应度地形中的位置，采用针对性策略指导种群进化。这种方法提高了进化过程的整体效率和有效性。

山底代表适应度值较低的个体，它们远离全局最优且具有显著的改进潜力。对这些个体采用增强全局探索的策略。该方法鼓励个体逃离当前区域以探索更广阔的搜索空间，从而增加解的多样性。

山脊区域包含适应度值中等的个体，它们逐渐接近全局最优解。由于山脊属于过渡阶段，个体可以通过进化算法向山顶移动，因此不需要额外策略。

山顶区域由适应度值较高的个体组成，它们接近或处于全局最优解。这些个体的重点应该是局部开发，通过精细搜索在当前最佳个体基础上寻找进一步的最优解。

指导策略基于个体在适应度地形中的位置使用差异化优化方法。通过动态调整算法的全局探索和局部开发能力，该方法增强了种群的适应性和多样性，最终找到影响力最大化问题的最优种子集。具体实现如算法\ref{alg:landscape_guided}所述。

\begin{algorithm}[H]
\caption{地形指导策略}
\label{alg:landscape_guided}
\begin{algorithmic}[1]
\REQUIRE 图$G = (V, E)$，种群$X$，种子集大小$k$，种群大小$pop$
\ENSURE 更新后的种群$X$
\FOR{$i=1$ to $pop$}
    \IF{$X_i$位于山底}
        \STATE $X_i = Q\text{-}value\_ascending\_search(G, k)$
    \ELSE
        \IF{$X_i$位于山顶}
            \STATE $X_i = Local\_search\_strategy(G, X_i)$
        \ENDIF
    \ENDIF
\ENDFOR
\RETURN $X$
\end{algorithmic}
\end{algorithm}

\subsection{局部搜索策略}

给定网络结构，候选种子集的期望影响力可能受到局部域的影响。局部搜索策略有潜力改善种子集的影响力。具体而言，局部搜索通过利用邻居节点生成新的候选种子集，并尝试用邻居集中的节点替换当前集合中的劣质节点。该策略的伪代码如算法\ref{alg:local_search}所示。

\begin{algorithm}[H]
\caption{局部搜索策略}
\label{alg:local_search}
\begin{algorithmic}[1]
\REQUIRE 图$G = (V, E)$，候选种子集$S$
\ENSURE 增强种子集$S^*$
\STATE $S^* = S$
\FOR{$u$ in $S$}
    \FOR{$v$ in $G.neighbor(u)$}
        \IF{$v$ not in $S^*$}
            \STATE 通过用$v$替换$S^*$中的$u$创建邻居解$S'$
            \IF{$EDV(S') > EDV(S^*)$}
                \STATE $S^* = S'$
            \ENDIF
        \ENDIF
    \ENDFOR
\ENDFOR
\RETURN $S^*$
\end{algorithmic}
\end{algorithm}

\section{状态感知的自适应机制}

\subsection{景观状态值计算}

ANFDE-IM算法的核心创新在于其景观状态感知机制。该机制通过计算景观状态值$\lambda$来实时监测种群在解空间中的分布特征。$\lambda$值反映了当前最优个体在种群中的相对位置，其计算公式如式(\ref{eq:lambda})所示：

\begin{equation}
\label{eq:lambda}
\lambda = \frac{d_g - d_{min}}{d_{max} - d_{min}}
\end{equation}

其中$d_g$表示当前最优个体与种群中其他个体的平均距离，$d_{max}$和$d_{min}$分别表示种群中个体间的最大和最小平均距离。

个体间距离采用基于LFV值的加权PDI距离计算：

\begin{equation}
\label{eq:pdi_distance}
d(S_i, S_j) = \frac{\sum_{v \in S_i \triangle S_j} LFV(v)}{\sum_{v \in S_i \cup S_j} LFV(v)}
\end{equation}

其中$S_i \triangle S_j$表示对称差集，$LFV(v) = |N(v)| \times p$为节点$v$的局部影响力值。

\subsection{动态状态判定}

基于$\lambda$值和动态阈值，算法将搜索过程划分为四种状态。动态阈值使用滑动窗口内$\lambda$值的四分位数计算：

\begin{align}
Q_1 &= \text{Percentile}_{25}(\lambda_{recent}) \label{eq:q1} \\
Q_3 &= \text{Percentile}_{75}(\lambda_{recent}) \label{eq:q3}
\end{align}

状态区间划分如下：

\begin{align}
\text{convergence} &: \lambda \in [0.0, Q_1] \label{eq:convergence} \\
\text{exploitation} &: \lambda \in (Q_1, \frac{Q_1+Q_3}{2}] \label{eq:exploitation} \\
\text{exploration} &: \lambda \in (\frac{Q_1+Q_3}{2}, Q_3] \label{eq:exploration} \\
\text{escape} &: \lambda \in (Q_3, 1.0] \label{eq:escape}
\end{align}

当满足逃逸条件时强制进入逃逸状态：

\begin{equation}
\label{eq:escape_condition}
\text{EscapeCondition} = (|\lambda| < 10^{-4}) \land (\text{stagnation\_counter} \geq \text{threshold})
\end{equation}

其中停滞阈值动态调整：$\text{threshold} = 3 + \lfloor \frac{gen}{50} \rfloor$。

\subsection{状态驱动的变异策略}

根据不同的景观状态，算法采用相应的变异策略：

\textbf{探索变异（DE/rand/2）：}适用于exploration状态，增强全局搜索能力：
\begin{equation}
\label{eq:exploration_mutation}
M_i = X_{r1} + F \cdot (X_{r2} - X_{r3}) + F \cdot (X_{r4} - X_{r5})
\end{equation}

\textbf{开发变异（DE/current-to-best/1）：}适用于exploitation状态，平衡探索与开发：
\begin{equation}
\label{eq:exploitation_mutation}
M_i = X_i + F \cdot (X_{best} - X_i) + F \cdot (X_{r1} - X_{r2})
\end{equation}

\textbf{收敛变异（DE/best/1）：}适用于convergence状态，专注局部精细搜索：
\begin{equation}
\label{eq:convergence_mutation}
M_i = X_{best} + F \cdot (X_{r1} - X_{r2})
\end{equation}

\textbf{逃逸变异：}适用于escape状态，基于历史优质解进行扰动：
\begin{equation}
\label{eq:escape_mutation}
M_i = X_i + \text{Perturbation}(\text{EscapePool})
\end{equation}

\subsection{自适应参数调整}

算法采用基于成功经验的参数自适应机制，动态调整交叉概率$CR$和缩放因子$F$的分布参数：

\begin{align}
\mu_{CR}^{(g+1)} &= (1-c) \cdot \mu_{CR}^{(g)} + c \cdot \text{mean}(\text{success\_CR}) \label{eq:cr_update} \\
\mu_F^{(g+1)} &= (1-c) \cdot \mu_F^{(g)} + c \cdot \text{mean}(\text{success\_F}) \label{eq:f_update}
\end{align}

其中$c = 0.1$为学习率，$\text{success\_CR}$和$\text{success\_F}$为成功产生改进解的参数集合。

每代进化中，参数按以下分布生成：
\begin{align}
CR &\sim \mathcal{N}(\mu_{CR}, 0.1^2) \text{ 截断到 } [0,1] \label{eq:cr_generation} \\
F &\sim \text{Cauchy}(\mu_F, 0.1) \text{ 截断到 } [0,1] \label{eq:f_generation}
\end{align}

\section{多层次局部搜索与多样性维护}

\subsection{邻域搜索策略}

算法采用基于邻居节点的局部搜索，针对种群中的优质个体进行精细优化。对于种子集中的每个节点$v$，选择其LFV值最高的前$N$个邻居：

\begin{equation}
\label{eq:top_neighbors}
\text{TopNeighbors}(v) = \text{TopK}(N(v), N, LFV)
\end{equation}

按节点LFV值升序遍历，尝试用高LFV值邻居替换：

\begin{equation}
\label{eq:neighbor_replacement}
S' = S \setminus \{v\} \cup \{u\}, \quad u \in \text{TopNeighbors}(v)
\end{equation}

仅当$EDV(S') > EDV(S)$时接受替换。

\subsection{多层次搜索机制}

算法实施三层次的局部搜索策略：

\begin{enumerate}
\item \textbf{种群局部搜索：}对前10\%优质个体进行邻域搜索，最大邻居数为8
\item \textbf{全局最优搜索：}对当前最优个体进行深度搜索，最大邻居数为10
\item \textbf{自适应搜索：}根据搜索成功率调整搜索强度
\end{enumerate}

\subsection{逃逸候选池管理}

维护一个逃逸候选池存储历史优质解，用于逃逸变异：

\begin{equation}
\label{eq:escape_pool}
\text{EscapePool} = \{\mathbf{x} : EDV(\mathbf{x}) \geq \text{Percentile}_{90}(\text{HistoryFitness})\}
\end{equation}

池大小限制为20个解，采用适应度排序的截断策略更新。

\subsection{多样性监测与增强}

算法通过计算种群中唯一解的比例来监测多样性：

\begin{equation}
\label{eq:diversity}
\text{Diversity} = \frac{|\{\text{unique solutions}\}|}{|\text{population}|}
\end{equation}

当多样性低于0.5时，触发多样性增强机制，通过桥节点优先的随机生成策略补充种群。

\section{ANFDE-IM主循环算法}

\begin{algorithm}[H]
\caption{ANFDE-IM主循环算法}
\label{alg:anfde_main}
\begin{algorithmic}[1]
\REQUIRE 初始种群$P$，种子集大小$k$，最大函数评估次数$FE_{max}$，传播概率$p$
\ENSURE 最优种子集$S^*$
\STATE $FE \leftarrow 0$, $gen \leftarrow 0$
\STATE 预计算LFV缓存：$lfv\_cache \leftarrow \{v: |N(v)| \times p \text{ for } v \in V\}$
\STATE $\lambda_0 \leftarrow \text{ComputeLambda}(P, lfv\_cache)$
\STATE 初始化参数：$\mu_{CR} = 0.5$, $\mu_F = 0.5$
\WHILE{$FE < FE_{max}$ AND $gen < 300$}
    \STATE $gen \leftarrow gen + 1$
    \STATE // 地形状态感知
    \STATE $\lambda_g \leftarrow \text{ComputeLambda}(P, lfv\_cache)$
    \STATE $state_g \leftarrow \text{DetermineState}(\lambda_g)$
    \STATE // 参数自适应更新
    \STATE 根据式(\ref{eq:cr_update})和(\ref{eq:f_update})更新$\mu_{CR}$和$\mu_F$
    \STATE 清空成功参数集合
    \STATE
    \STATE // 差分进化操作
    \STATE $P_{new} \leftarrow []$
    \FOR{$i = 1$ to $|P|$}
        \STATE 根据式(\ref{eq:cr_generation})和(\ref{eq:f_generation})生成$(CR, F)$
        \STATE $\mathbf{u}_i \leftarrow \text{SelectMutation}(P[i], state_g, F)$
        \STATE $\mathbf{v}_i \leftarrow \text{Crossover}(P[i], \mathbf{u}_i, CR)$
        \STATE $P[i] \leftarrow \text{Selection}(P[i], \mathbf{v}_i, CR, F)$
        \STATE $P_{new}.\text{append}(P[i])$
    \ENDFOR
    \STATE $P \leftarrow P_{new}$
    \STATE
    \STATE // 多层次局部搜索
    \STATE $candidates \leftarrow \text{SelectTop10Percent}(P)$
    \FOR{$\mathbf{x} \in candidates$}
        \STATE $\mathbf{x}' \leftarrow \text{LocalSearch}(\mathbf{x}, max\_neighbors=8)$
        \IF{$EDV(\mathbf{x}') > EDV(\mathbf{x})$}
            \STATE 在$P$中替换$\mathbf{x}$为$\mathbf{x}'$
        \ENDIF
    \ENDFOR
    \STATE
    \STATE // 全局最优优化
    \STATE $\mathbf{x}_{best} \leftarrow \arg\max_{\mathbf{x} \in P} EDV(\mathbf{x})$
    \STATE $\mathbf{x}_{best}' \leftarrow \text{LocalSearch}(\mathbf{x}_{best}, max\_neighbors=10)$
    \IF{$EDV(\mathbf{x}_{best}') > EDV(\mathbf{x}_{best})$}
        \STATE 更新全局最优解
    \ENDIF
    \STATE
    \STATE // 多样性维护
    \STATE $diversity \leftarrow \text{ComputeDiversity}(P)$
    \IF{$diversity < 0.5$}
        \STATE $P \leftarrow \text{DiversityEnhancement}(P)$
    \ENDIF
    \STATE
    \STATE // 逃逸候选池更新
    \STATE 根据式(\ref{eq:escape_pool})更新逃逸候选池
\ENDWHILE
\RETURN $\arg\max_{\mathbf{x} \in P} EDV(\mathbf{x})$
\end{algorithmic}
\end{algorithm}

\section{算法复杂度分析}

\subsection{时间复杂度}

ANFDE-IM算法主循环每代的时间复杂度分析如下：

\begin{itemize}
\item LFV缓存预计算：$O(|V|)$（仅首次）
\item 景观状态计算：$O(N^2 \cdot k)$（加权PDI距离计算）
\item 差分进化操作：$O(N \cdot k)$
\item 适应度评估：$O(N \cdot k \cdot \bar{d})$
\item 局部搜索：$O(N \cdot p_{ls} \cdot k^2 \cdot \bar{d})$
\end{itemize}

其中$N$为种群大小，$k$为种子集大小，$\bar{d}$为平均度数，$p_{ls}$为局部搜索概率。

总体时间复杂度为：
\begin{equation}
T(n) = O(G \cdot N \cdot (N \cdot k + k \cdot \bar{d} + p_{ls} \cdot k^2 \cdot \bar{d}))
\end{equation}

其中$G$为最大迭代次数。

\subsection{空间复杂度}

主要空间开销包括：
\begin{itemize}
\item 种群存储：$O(N \cdot k)$
\item LFV缓存：$O(|V|)$
\item 逃逸候选池：$O(20 \cdot k)$
\item 历史记录：$O(G)$
\end{itemize}

总体空间复杂度为$O(N \cdot k + |V| + G)$。

\section{算法特性分析}

ANFDE-IM算法具有以下显著特性：

\textbf{自适应性：}通过景观状态感知实时调整搜索策略，参数自适应机制学习最优配置。

\textbf{平衡性：}状态驱动的变异策略在全局探索与局部开发之间实现动态平衡。

\textbf{鲁棒性：}多层次的局部搜索和多样性维护机制确保算法在各种网络结构下稳定工作。

\textbf{高效性：}加权PDI距离计算和LFV缓存机制显著提高计算效率。

\end{document}
