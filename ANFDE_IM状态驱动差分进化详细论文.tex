\documentclass[12pt]{article}
\usepackage[utf8]{inputenc}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{xeCJK}
\usepackage{geometry}
\geometry{a4paper,margin=2.5cm}

\title{状态驱动的差分进化算法详细设计}
\author{研究团队}
\date{\today}

\begin{document}

\maketitle

\section{状态驱动的差分进化算法}

\subsection{自适应参数生成}

为适应搜索过程中种群状态的动态变化，本研究引入了参数自适应机制。每一代中，每个个体的缩放因子$F_i$和交叉概率$Cr_i$按如下方式生成：

\begin{align}
F_i &\sim \text{Cauchy}(\mu_F, 0.1), \quad F_i \in [0, 1] \label{eq:f_generation} \\
Cr_i &\sim \mathcal{N}(\mu_{Cr}, 0.1), \quad Cr_i \in [0, 1] \label{eq:cr_generation}
\end{align}

进化过程中，将每代成功个体的参数值存储在历史记忆中，并利用这些历史信息对参数均值按如下方式进行指数平滑更新：

\begin{align}
\mu_{Cr} &= (1-c)\mu_{Cr} + c \cdot \text{mean}_A(S_{Cr}) \label{eq:cr_mean_update} \\
\mu_F &= (1-c)\mu_F + c \cdot \text{mean}_L(S_F) \label{eq:f_mean_update}
\end{align}

其中，$S_{Cr}$和$S_F$为历史记忆中成功的$Cr$和$F$值，$c$为平滑系数。该机制能够动态调整参数分布，提高算法适应性和搜索性能。

参数生成过程采用拒绝采样确保参数在有效范围内：

\begin{algorithm}[H]
\caption{自适应参数生成}
\label{alg:parameter_generation}
\begin{algorithmic}[1]
\REQUIRE 当前分布均值$\mu_{CR}$，$\mu_F$
\ENSURE 参数对$(CR, F)$
\STATE // 生成交叉概率CR
\WHILE{True}
    \STATE $CR \leftarrow \mathcal{N}(\mu_{CR}, 0.1^2)$
    \IF{$0 \leq CR \leq 1$}
        \STATE \textbf{break}
    \ENDIF
\ENDWHILE
\STATE // 生成缩放因子F
\WHILE{True}
    \STATE $F \leftarrow \text{Cauchy}(\mu_F, 0.1)$
    \IF{$0 \leq F \leq 1$}
        \STATE \textbf{break}
    \ENDIF
\ENDWHILE
\STATE 记录生成的参数到历史列表
\RETURN $(CR, F)$
\end{algorithmic}
\end{algorithm}

\subsubsection{成功参数收集}

在选择操作中，当试验个体优于目标个体时，记录对应的成功参数：

\begin{align}
\text{success\_CR} &\leftarrow \text{success\_CR} \cup \{CR\} \label{eq:success_cr_collection} \\
\text{success\_F} &\leftarrow \text{success\_F} \cup \{F\} \label{eq:success_f_collection}
\end{align}

每代开始时清空成功参数集合，确保参数学习基于当代的搜索反馈。

\subsection{状态感知变异策略}

差分进化算法以个体差异向量驱动搜索，通过多样化变异算子灵活兼顾全局探索与局部开发。经典变异策略包括DE/rand/1、DE/best/1、DE/rand/2、DE/best/2及DE/current-to-best/1等。各变异算子在全局探索与局部开发上各有侧重，适用于不同搜索阶段。结合地形状态感知，本文选取四种互补的变异算子。

在收敛状态，种群已聚集于单一吸引盆地，适应度梯度趋于平缓，局部邻域蕴含的可提升空间有限，此时采用DE/best/1等以全局最优为基准的局部扰动算子，能够充分利用已有梯度信息，实现对盆地中心的细粒度逼近，提高局部开发能力并加速收敛。经典变异公式为：
\begin{equation}
\label{eq:classic_de_best_1}
V_i^G = X_{best}^G + F \cdot (X_{r2}^G - X_{r3}^G)
\end{equation}

本文将其变异操作设计为：以当前全局最优个体$X_{best}$为基点，选取两组不同个体$X_{r1}, X_{r2}$，构造差分集$D = X_{r1} \setminus X_{r2}$，据此将$X_{best}$中局部影响（LFV）值最低的节点用扰动集内节点逐一替换，替换数量为$\lfloor F|D| \rfloor$，如遇重复节点则通过$R(\cdot)$修复。最终离散变异算子如下：
\begin{equation}
\label{eq:convergence_mutation}
M_i = R(X_{best} \oplus (F \cdot (X_{r1} \ominus X_{r2})))
\end{equation}

其中$F$为自适应缩放因子，$\oplus$表示按扰动集依次替换局部影响力最低节点，$R(\cdot)$为冗余修复（去重补全）。

开发状态下，种群分布于盆地边缘，地形表现为梯度过渡区，存在通向更优吸引域的潜在路径。此时梯度方向的确定性较低，采用DE/current-to-best/1融合了个体自适应性、全局最优引导和随机扰动，能够实现对复杂地形边界的高效探索，既防止早熟收敛，又保证对有利方向的利用。经典DE/current-to-best/1为：
\begin{equation}
\label{eq:classic_de_current_to_best_1}
V_i^G = X_i^G + F \cdot (X_{best}^G - X_i^G) + F \cdot (X_{r1}^G - X_{r2}^G)
\end{equation}

本文以当前个体$X_i$为基点，分别构造两个差分集$D_1 = X_{best} \setminus X_i$和$D_2 = X_{r1} \setminus X_{r2}$，先后用各自扰动集内节点替换$X_i$中LFV值最低的节点，替换数量分别为$\lfloor F|D_1| \rfloor$和$\lfloor F|D_2| \rfloor$。如有重复节点，则通过$R(\cdot)$修复。最终变异公式为：
\begin{equation}
\label{eq:exploitation_mutation}
M_i = R(X_i \oplus (F \cdot (X_{best} \ominus X_i)) \oplus (F \cdot (X_{r1} \ominus X_{r2})))
\end{equation}

探索状态对应多吸引盆地的广泛分布，适应度地形高度复杂且多峰，局部相关性弱。此时，过度依赖全局最优或个体历史可能导致搜索受限于局部结构，DE/rand/2等强探索算子通过高维扰动和多源引导在解空间中实现大步跳跃，有效提升种群多样性与全局搜索能力，从而增强跳出局部最优的概率。经典DE/rand/2为：
\begin{equation}
\label{eq:classic_de_rand_2}
V_i^G = X_{r1}^G + F \cdot (X_{r2}^G - X_{r3}^G) + F \cdot (X_{r4}^G - X_{r5}^G)
\end{equation}

本文随机选取五个不同个体$X_{r1}, X_{r2}, X_{r3}, X_{r4}, X_{r5}$，分别构造两个差分集$D_1 = X_{r2} \setminus X_{r3}$，$D_2 = X_{r4} \setminus X_{r5}$，将$X_{r1}$中LFV值最低的节点依次用扰动集节点替换，替换总数为$\min(k, \lfloor F|D_1| \rfloor + \lfloor F|D_2| \rfloor)$，如有重复则$R(\cdot)$修复。最终变异公式为：
\begin{equation}
\label{eq:exploration_mutation}
M_i = R(X_{r1} \oplus (F \cdot (X_{r2} \ominus X_{r3})) \oplus (F \cdot (X_{r4} \ominus X_{r5})))
\end{equation}

而在逃逸状态，种群在某一地形结构长时间停滞，适应度未见提升，地形表现为极小梯度或平坦区域。此时单纯依赖启发性搜索难以突破瓶颈，需引入基于历史优解或随机扰动的跳跃机制，以实现跨越地形障碍，重新激发全局搜索活性。

本研究通过逃逸候选池机制以推动种群跳出局部盆地区域。具体地，对当前种群中的个体，计算其每个节点的一阶邻居数量与传播概率的乘积（记为F值，反映节点在单步传播下的影响力），并将这些节点按F值从低到高排序。随后，从全图中筛选出度中心性大于全局平均水平且当前解未包含的节点作为候选替换集，替换节点数由$\min(\lfloor F \times k \rfloor, k)$确定，用高中心性节点依次替换当前解中F值较低的节点，生成新的解。若该新解适应度优于原解，则将其纳入逃逸候选池，为后续逃逸变异操作提供基础。

随后逃逸变异从逃逸候选池中随机选取两个不同的解$x_1, x_2$，构造差分集$D = x_1 \setminus x_2$，用$D$中节点随机替换当前个体$X_i$中的节点，替换数量为$\min(k, \lfloor F|D| \rfloor)$，如有重复节点则通过$R(\cdot)$修复。逃逸变异公式为：
\begin{equation}
\label{eq:escape_mutation}
M_i = R(X_i \oplus (F \cdot (x_1 \ominus x_2)))
\end{equation}

基于地形状态感知对变异算子的自适应调度，根本上是针对适应度地形结构与搜索行为耦合关系的优化。通过在微观（个体间差分）、局部（梯度/盆地结构）与宏观（全局分布）层面动态调整搜索算子，实现了算法对多样复杂环境的普适适应与全局最优能力的提升。

\begin{algorithm}[H]
\caption{状态感知变异策略}
\label{alg:state_aware_mutation_strategy}
\begin{algorithmic}[1]
\REQUIRE 个体$X_i$，当前状态$state$，种群$P$，缩放因子$F$，种子集大小$k$，逃逸候选池$EscapePool$
\ENSURE 变异个体$M_i$
\STATE // 根据地形状态选择相应的变异算子
\IF{$state = \text{convergence}$}
    \STATE 随机选择$X_{r1}, X_{r2} \in P$，且$X_{r1} \neq X_{r2}$
    \STATE $D \leftarrow X_{r1} \ominus X_{r2}$ // 构造差分集
    \STATE $replace\_count \leftarrow \lfloor F \times |D| \rfloor$
    \STATE $M_i \leftarrow X_{best}.\text{copy}()$
    \FOR{$j = 1$ to $replace\_count$}
        \STATE $min\_node \leftarrow \arg\min_{v \in M_i} \text{LFV}(v)$ // 选择LFV最小节点
        \STATE $new\_node \leftarrow \text{RandomChoice}(D)$ // 从差分集随机选择
        \STATE 替换$M_i$中的$min\_node$为$new\_node$
        \STATE $D \leftarrow D \setminus \{new\_node\}$ // 移除已使用节点
    \ENDFOR
\ELSIF{$state = \text{exploitation}$}
    \STATE 随机选择$X_{r1}, X_{r2} \in P$，且$X_{r1} \neq X_{r2}$
    \STATE $D_1 \leftarrow X_{best} \ominus X_i$，$D_2 \leftarrow X_{r1} \ominus X_{r2}$
    \STATE $replace\_count_1 \leftarrow \lfloor F \times |D_1| \rfloor$
    \STATE $replace\_count_2 \leftarrow \lfloor F \times |D_2| \rfloor$
    \STATE $M_i \leftarrow X_i.\text{copy}()$
    \STATE 按上述替换规则依次处理$D_1$和$D_2$
\ELSIF{$state = \text{exploration}$}
    \STATE 随机选择$X_{r1}, X_{r2}, X_{r3}, X_{r4}, X_{r5} \in P$，且互不相同
    \STATE $D_1 \leftarrow X_{r2} \ominus X_{r3}$，$D_2 \leftarrow X_{r4} \ominus X_{r5}$
    \STATE $combined\_D \leftarrow D_1 \cup D_2$ // 组合差分集
    \STATE $replace\_count \leftarrow \min(k, \lfloor F \times |combined\_D| \rfloor)$
    \STATE $M_i \leftarrow X_{r1}.\text{copy}()$
    \STATE 按上述替换规则处理$combined\_D$
\ELSIF{$state = \text{escape}$}
    \IF{$|EscapePool| \geq 2$}
        \STATE 从逃逸候选池中随机选择$x_1, x_2$
        \STATE $D \leftarrow x_1 \ominus x_2$ // 构造逃逸差分集
        \STATE $replace\_count \leftarrow \min(k, \lfloor F \times |D| \rfloor)$
        \STATE $M_i \leftarrow X_i.\text{copy}()$
        \STATE 随机选择$replace\_count$个位置进行替换
    \ELSE
        \STATE $M_i \leftarrow X_i$ // 候选池不足时返回原个体
    \ENDIF
\ENDIF
\STATE $M_i \leftarrow R(M_i)$ // 重复节点修复
\RETURN $M_i$
\end{algorithmic}
\end{algorithm}


\begin{algorithm}[H]
\caption{状态感知变异策略}
\label{alg:state_aware_mutation_strategy}
\begin{algorithmic}[1]
\REQUIRE 个体$X_i$，状态$state$，种群$P$，缩放因子$F$，种子集大小$k$，逃逸候选池$EscapePool$
\ENSURE 变异个体$M_i$
\IF{$state = \textbf{convergence}$}
    \STATE 取$X_{best}$，随机$X_{r1}, X_{r2} \in P, X_{r1} \neq X_{r2}$
    \STATE 差分集$D \gets X_{r1} \ominus X_{r2}$
    \STATE $replace\_count \gets \lfloor F \cdot |D| \rfloor$
    \STATE $M_i \gets X_{best}$
    \STATE 按LFV递增依次用$D$中节点替换$M_i$的$replace\_count$个节点
\ELSIF{$state = \textbf{exploitation}$}
    \STATE $D_1 \gets X_{best} \ominus X_i, D_2 \gets X_{r1} \ominus X_{r2}$
    \STATE $count_1, count_2 \gets \lfloor F|D_1| \rfloor, \lfloor F|D_2| \rfloor$
    \STATE $M_i \gets X_i$
    \STATE 依次用$D_1, D_2$中节点替换LFV最小的节点，共$count_1 + count_2$次
\ELSIF{$state = \textbf{exploration}$}
    \STATE $D_1 \gets X_{r2} \ominus X_{r3}, D_2 \gets X_{r4} \ominus X_{r5}$
    \STATE $replace\_count \gets \min(k, \lfloor F|D_1| \rfloor + \lfloor F|D_2| \rfloor)$
    \STATE 依次用$D_1, D_2$替换
\ELSIF{$state = \textbf{escape}$}
    \IF{$|EscapePool| \geq 2$}
        \STATE 取$x_1, x_2 \in EscapePool$
        \STATE $D \gets x_1 \ominus x_2$
        \STATE $replace\_count \gets \min(k, \lfloor F|D| \rfloor)$
        \STATE $M_i \gets X_i$
        \STATE 随机$replace\_count$位置用$D$节点替换
    \ELSE
        \STATE $M_i \gets X_i$
    \ENDIF
\ENDIF
\STATE $M_i \gets R(M_i)$ // 冗余修复
\RETURN $M_i$
\end{algorithmic}
\end{algorithm}


算法中的替换操作遵循以下原则：优先选择当前个体中LFV值最小的节点进行替换，确保用高影响力节点替换低影响力节点。替换数量由$\lfloor F \times |D| \rfloor$控制，其中$|D|$为差分集大小。修复函数$R(\cdot)$负责处理重复节点问题，通过度中心性高于平均水平的未选节点进行补全，确保解的有效性和质量。四种变异算子的差异化设计实现了从局部精细搜索到全局跳跃搜索的完整覆盖，为算法在不同搜索阶段提供了适配的搜索策略。

\subsubsection{状态感知的变异算子映射}

基于盆地间跳跃公式，ANFDE算法设计以下四种变异算子，每种算子对应特定的地形状态：

\textbf{1. 收敛状态变异算子（DE/best/1）：}

当种群集中在一个吸引盆地时，算法以全局最优解为基准，开发当前盆地：

\begin{equation}
\label{eq:convergence_mutation_discrete}
M_i = X_{best} \oplus F \cdot (X_{r1} \ominus X_{r2})
\end{equation}

其中：
\begin{itemize}
\item $X_{best}$：当前种群中最优的个体
\item 差异操作：$X_{r1} \ominus X_{r2} = \{x \mid x \in X_{r1} \text{ and } x \notin X_{r2}\}$
\item 替换数量：$N = F \cdot |X_{r1} \ominus X_{r2}|$
\item 替换操作$\oplus$：用差异集中的节点替换$X_{best}$中LFV值最小的节点
\end{itemize}

\textbf{2. 开发状态变异算子（DE/current-to-best/1）：}

当种群处于过渡区域时，算法结合当前解和全局最优解，引导种群跳跃到更优区域：

\begin{equation}
\label{eq:exploitation_mutation_discrete}
M_i = X_i \oplus F \cdot (X_{best} \ominus X_i) \oplus F \cdot (X_{r1} \ominus X_{r2})
\end{equation}

其中：
\begin{itemize}
\item $X_i$：当前个体
\item 差异操作：
  \begin{align}
  X_{best} \ominus X_i &= \{x \mid x \in X_{best} \text{ and } x \notin X_i\} \\
  X_{r1} \ominus X_{r2} &= \{x \mid x \in X_{r1} \text{ and } x \notin X_{r2}\}
  \end{align}
\item 替换数量：分别计算两个差异集的替换数量
\item 替换操作$\oplus$：用两个差异集中的节点替换$X_i$中LFV值最小的节点
\end{itemize}

\textbf{3. 探索状态变异算子（DE/rand/2）：}

当种群分布在多个吸引盆地时，算法采用随机化策略，扩大种群的覆盖范围：

\begin{equation}
\label{eq:exploration_mutation_discrete}
M_i = X_{r1} \oplus F \cdot (X_{r2} \ominus X_{r3}) \oplus F \cdot (X_{r4} \ominus X_{r5})
\end{equation}

其中：
\begin{itemize}
\item $X_{r1}$的生成：$X_{r1} = X.\text{get}(\text{random}(\text{pop}))$
\item 差异操作：
  \begin{align}
  X_{r2} \ominus X_{r3} &= \{x \mid x \in X_{r2} \text{ and } x \notin X_{r3}\} \\
  X_{r4} \ominus X_{r5} &= \{x \mid x \in X_{r4} \text{ and } x \notin X_{r5}\}
  \end{align}
\item 替换数量：$N = F \cdot |\text{combined\_difference\_set}|$
\item 替换操作$\oplus$：用两个差异集中的节点替换$X_{r1}$中LFV值最小的节点
\end{itemize}

\textbf{4. 逃逸机制：}

当种群长期停滞时，算法通过逃逸候选池辅助跳跃到新的盆地：

\begin{equation}
\label{eq:escape_mutation_discrete}
M_i = X_i \oplus (\mu_F \cdot (x_1 \ominus x_2))
\end{equation}

其中：
\begin{itemize}
\item 差异操作：$(x_1 \ominus x_2) = \{x \mid x \in x_1 \text{ and } x \notin x_2\}$
\item 替换操作$\oplus$：用差异集中的节点替换$X_i$中的节点
\item $x_1, x_2$：从逃逸候选池中随机选择的两个解
\end{itemize}

\textbf{5. 替换数量计算：}

对于逃逸变异，替换数量计算如下：
\begin{equation}
\label{eq:escape_replacement_count}
N = \min(k, \lfloor |x_1 \ominus x_2| \rfloor)
\end{equation}

其中：
\begin{itemize}
\item $|x_1 \ominus x_2|$表示差异集的大小
\item $k$是种子集大小
\item $\lfloor \cdot \rfloor$表示向下取整
\end{itemize}

\subsubsection{变异算子的搜索特性分析}

四种变异算子在搜索行为上呈现出明显的差异化特征，共同构成了完整的自适应搜索策略体系。DE/best/1算子表现出强开发、弱探索的特性，其搜索行为以最优解为中心进行局部搜索，搜索半径主要由缩放因子$F$控制。这种特性使其特别适合在已识别的优质区域进行精细开发，能够充分挖掘局部最优解的潜力。

DE/current-to-best/1算子在探索与开发之间实现了良好的平衡。该算子结合当前位置和最优位置的信息，既具有向最优解靠近的方向性，又通过随机扰动项保持了必要的探索能力。这种平衡特性使其成为过渡阶段的理想选择，能够在引导种群向优质区域移动的同时，避免过快的收敛。

DE/rand/2算子展现出强探索、弱开发的搜索特性。通过使用多个随机选择的个体，该算子能够产生强烈的随机扰动，覆盖更大的搜索空间。这种特性对于维持种群多样性、避免过早收敛具有重要意义，特别适用于种群分散分布的探索阶段。

逃逸机制作为一种特殊的跳跃性搜索策略，其设计目标是帮助算法跳出局部最优陷阱。该机制利用历史优质解之间的差异信息，为当前搜索提供新的方向指导。与传统DE算子不同，逃逸机制不依赖于当前种群的分布状态，而是通过历史信息提供跳跃到新搜索区域的能力，从而避免算法陷入长期停滞。

这四种算子的搜索特性形成了从局部精细搜索到全局跳跃搜索的完整谱系。在实际应用中，算法根据地形状态感知的结果，动态选择最适合当前搜索阶段的变异算子，从而实现了探索与开发的自适应平衡，提高了算法在复杂优化问题上的求解性能。

\subsubsection{离散变异操作的统一框架}

所有变异算子都遵循统一的离散变异框架：

\begin{algorithm}[H]
\caption{离散变异操作统一框架}
\label{alg:discrete_mutation_framework}
\begin{algorithmic}[1]
\REQUIRE 个体$X_i$，差异集$\text{difference\_set}$，缩放因子$F$
\ENSURE 变异个体$\text{mutant}$
\STATE $\text{mutant} \leftarrow X_i.\text{copy}()$
\STATE $N_{replace} \leftarrow \lfloor F \times |\text{difference\_set}| \rfloor$
\FOR{$j = 1$ to $N_{replace}$}
    \IF{$\text{difference\_set} \neq \emptyset$}
        \STATE $\text{replacement\_node} \leftarrow \text{RandomChoice}(\text{difference\_set})$
        \STATE $\text{min\_lfv\_node} \leftarrow \arg \min_{v \in \text{mutant}} \text{LFV}(v)$
        \STATE $\text{mutant}[\text{mutant}.\text{index}(\text{min\_lfv\_node})] \leftarrow \text{replacement\_node}$
        \STATE $\text{difference\_set}.\text{remove}(\text{replacement\_node})$
    \ELSE
        \STATE \textbf{break} // 差异集为空时停止替换
    \ENDIF
\ENDFOR
\RETURN $\text{RepairDuplicates}(\text{mutant})$
\end{algorithmic}
\end{algorithm}

\begin{algorithm}[H]
\caption{状态感知变异算子选择}
\label{alg:state_aware_mutation}
\begin{algorithmic}[1]
\REQUIRE 个体$x_i$，当前状态$state$，种子集大小$k$
\ENSURE 变异个体$u_i$
\IF{$state = \text{escape}$}
    \STATE $u_i \leftarrow \text{EscapeMutation}(x_i, k)$
\ELSIF{$state = \text{convergence}$}
    \STATE $u_i \leftarrow \text{ConvergenceMutation}(x_i, k)$
\ELSIF{$state = \text{exploitation}$}
    \STATE $u_i \leftarrow \text{ExploitationMutation}(x_i, k)$
\ELSIF{$state = \text{exploration}$}
    \STATE $u_i \leftarrow \text{ExplorationMutation}(x_i, k)$
\ENDIF
\RETURN $\text{RepairDuplicates}(u_i)$
\end{algorithmic}
\end{algorithm}

\subsection{交叉策略}

ANFDE-IM算法针对离散优化问题的特点，设计了两种交叉策略：基础二项式交叉和动态交叉。

\subsubsection{基础二项式交叉}

基础交叉操作采用标准的二项式交叉机制：

\begin{equation}
\label{eq:basic_crossover}
\text{trial}[i] = \begin{cases}
\text{mutant}[i], & \text{if } \text{random}() < CR \\
\text{target}[i], & \text{otherwise}
\end{cases}
\end{equation}

交叉后检查重复节点并进行修复：

\begin{algorithm}[H]
\caption{基础二项式交叉}
\label{alg:basic_crossover}
\begin{algorithmic}[1]
\REQUIRE 目标个体$target$，变异个体$mutant$，交叉概率$CR$，种子集大小$k$
\ENSURE 试验个体$trial$
\STATE $trial \leftarrow target.\text{copy}()$
\FOR{$i = 0$ to $k-1$}
    \IF{$\text{random}() < CR$}
        \STATE $trial[i] \leftarrow mutant[i]$
    \ENDIF
\ENDFOR
\IF{$|\text{set}(trial)| \neq |trial|$}
    \STATE $trial \leftarrow \text{RepairDuplicates}(trial)$
\ENDIF
\RETURN $trial$
\end{algorithmic}
\end{algorithm}

\subsubsection{动态交叉策略}

动态交叉策略考虑了节点间的邻接关系，通过引入共同邻居节点增强解的连通性：

\begin{algorithm}[H]
\caption{动态交叉策略}
\label{alg:dynamic_crossover}
\begin{algorithmic}[1]
\REQUIRE 目标个体$target$，变异个体$mutant$，交叉概率$CR$，种子集大小$k$
\ENSURE 试验个体$trial$
\STATE $trial \leftarrow target.\text{copy}()$
\STATE $target\_set \leftarrow \text{set}(target)$
\STATE $mutant\_set \leftarrow \text{set}(mutant)$
\STATE // 计算共同邻居
\STATE $common\_neighbors \leftarrow \emptyset$
\FOR{$node \in target\_set \cap mutant\_set$}
    \STATE $common\_neighbors \leftarrow common\_neighbors \cup \text{neighbors}(node)$
\ENDFOR
\STATE $common\_neighbors \leftarrow common\_neighbors - (target\_set \cup mutant\_set)$
\STATE // 自适应交叉
\FOR{$i = 0$ to $k-1$}
    \IF{$\text{random}() < CR$}
        \STATE $trial[i] \leftarrow mutant[i]$
        \IF{$common\_neighbors \neq \emptyset$ AND $\text{random}() < 0.2$}
            \STATE $neighbor \leftarrow \text{RandomChoice}(common\_neighbors)$
            \STATE $trial[i] \leftarrow neighbor$
            \STATE $common\_neighbors.\text{remove}(neighbor)$
        \ENDIF
    \ENDIF
\ENDFOR
\RETURN $trial$
\end{algorithmic}
\end{algorithm}

\subsubsection{重复节点修复机制}

由于离散优化问题的特殊性，交叉操作可能产生重复节点。算法采用智能修复策略：

\begin{algorithm}[H]
\caption{重复节点修复}
\label{alg:repair_duplicates}
\begin{algorithmic}[1]
\REQUIRE 含重复节点的个体$individual$
\ENSURE 修复后的个体$fixed\_individual$
\STATE 统计节点出现次数
\STATE $repeated\_nodes \leftarrow$ 出现次数大于1的节点
\STATE $missing\_nodes \leftarrow$ 图中未在个体中出现的节点
\FOR{$repeated\_node \in repeated\_nodes$}
    \STATE // 选择度数大于平均值的节点作为替换
    \STATE $replacement \leftarrow$ 从$missing\_nodes$中选择度数最大的节点
    \STATE 替换$individual$中的重复节点
    \STATE $missing\_nodes.\text{remove}(replacement)$
\ENDFOR
\RETURN $individual$
\end{algorithmic}
\end{algorithm}

\subsection{局部搜索策略}

ANFDE-IM算法采用多层次的局部搜索策略，通过精细的邻域搜索提高解的质量。局部搜索基于预计算的高质量邻居信息，实现高效的局部优化。

\subsubsection{邻居预计算机制}

为提高局部搜索效率，算法在初始化阶段预计算每个节点的Top-20高LFV值邻居：

\begin{equation}
\label{eq:top_neighbors_precompute}
\text{top\_neighbors}[v] = \text{TopK}(\text{neighbors}(v), 20, LFV)
\end{equation}

其中$LFV(u) = |\text{neighbors}(u)| \times p$为节点$u$的局部影响力值。

预计算过程：

\begin{algorithm}[H]
\caption{邻居预计算}
\label{alg:neighbor_precompute}
\begin{algorithmic}[1]
\REQUIRE 图$G$，传播概率$p$
\ENSURE 邻居字典$top\_neighbors$
\STATE $top\_neighbors \leftarrow \{\}$
\FOR{$node \in V(G)$}
    \STATE $neighbors \leftarrow \text{neighbors}(node)$
    \STATE $sorted\_neighbors \leftarrow \text{Sort}(neighbors, \text{key}=LFV, \text{reverse}=\text{True})$
    \STATE $top\_neighbors[node] \leftarrow sorted\_neighbors[:20]$
\ENDFOR
\RETURN $top\_neighbors$
\end{algorithmic}
\end{algorithm}

\subsubsection{基于邻居的局部搜索}

局部搜索采用贪心策略，优先替换低影响力节点：

\begin{equation}
\label{eq:local_search_sorting}
\text{sorted\_nodes} = \text{Sort}(\text{individual}, \text{key}=\lambda n: LFV[n])
\end{equation}

对每个节点尝试用其高LFV值邻居替换：

\begin{equation}
\label{eq:neighbor_replacement}
\text{new\_individual} = (\text{individual} \setminus \{v\}) \cup \{u\}
\end{equation}

其中$u \in \text{top\_neighbors}[v]$且$u \notin \text{individual}$。

\begin{algorithm}[H]
\caption{基于邻居的局部搜索}
\label{alg:neighbor_based_local_search}
\begin{algorithmic}[1]
\REQUIRE 个体$individual$，最大邻居数$max\_neighbors$，搜索类型$type\_str$
\ENSURE 优化后的个体$best\_individual$
\STATE $best\_individual \leftarrow individual.\text{copy}()$
\STATE $best\_fitness \leftarrow \text{EDV}(best\_individual)$
\STATE $found\_improvement \leftarrow \text{False}$
\STATE // 按LFV值升序排序，优先替换低影响力节点
\STATE $sorted\_nodes \leftarrow \text{Sort}(individual, \text{key}=\lambda n: LFV[n])$
\FOR{$node \in sorted\_nodes$}
    \STATE $neighbors \leftarrow top\_neighbors[node][:max\_neighbors]$
    \FOR{$neighbor \in neighbors$}
        \IF{$neighbor \in best\_individual$}
            \STATE \textbf{continue}
        \ENDIF
        \STATE // 生成新解并评估
        \STATE $new\_individual \leftarrow [n \text{ if } n \neq node \text{ else } neighbor \text{ for } n \text{ in } best\_individual]$
        \STATE $new\_fitness \leftarrow \text{EDV}(new\_individual)$
        \IF{$new\_fitness > best\_fitness$}
            \STATE $best\_individual \leftarrow new\_individual$
            \STATE $best\_fitness \leftarrow new\_fitness$
            \STATE $found\_improvement \leftarrow \text{True}$
            \STATE \textbf{break} // 找到改进即跳出，加速搜索
        \ENDIF
    \ENDFOR
\ENDFOR
\STATE 更新搜索统计信息
\RETURN $best\_individual$
\end{algorithmic}
\end{algorithm}

\subsubsection{多层次搜索机制}

算法实施三层次的局部搜索策略，针对不同类型的个体采用不同的搜索强度：

\textbf{1. 种群局部搜索}

对前10\%优质个体进行邻域搜索，最大邻居数设为8：

\begin{equation}
\label{eq:population_local_search}
\text{candidates} = \text{SelectTop}(P, 0.1 \times |P|)
\end{equation}

\begin{algorithm}[H]
\caption{种群局部搜索}
\label{alg:population_local_search}
\begin{algorithmic}[1]
\REQUIRE 种群$P$
\ENSURE 优化后的种群$P'$
\STATE $num\_to\_optimize \leftarrow \max(1, \lfloor 0.1 \times |P| \rfloor)$
\STATE $candidates \leftarrow P[:num\_to\_optimize]$ // 已按适应度排序
\STATE $optimized \leftarrow []$
\FOR{$candidate \in candidates$}
    \STATE $optimized\_candidate \leftarrow \text{LocalSearch}(candidate, 8, \text{"spart"})$
    \STATE $optimized.\text{append}(optimized\_candidate)$
\ENDFOR
\STATE // 替换原个体（仅保留改进解）
\FOR{$i = 0$ to $|optimized|-1$}
    \IF{$\text{EDV}(optimized[i]) > \text{EDV}(candidates[i])$}
        \STATE $P[i] \leftarrow optimized[i]$
    \ENDIF
\ENDFOR
\RETURN $P$
\end{algorithmic}
\end{algorithm}

\textbf{2. 全局最优搜索}

对当前最优个体进行深度搜索，最大邻居数设为10：

\begin{algorithm}[H]
\caption{全局最优搜索}
\label{alg:global_best_search}
\begin{algorithmic}[1]
\REQUIRE 当前最优个体$G_{best}$
\ENSURE 优化后的最优个体$G_{best}'$
\STATE $current\_fitness \leftarrow \text{EDV}(G_{best})$
\STATE $optimized \leftarrow \text{LocalSearch}(G_{best}, 10, \text{"gbest"})$
\STATE $optimized\_fitness \leftarrow \text{EDV}(optimized)$
\IF{$optimized\_fitness > current\_fitness$}
    \STATE $G_{best}' \leftarrow optimized$
    \STATE 输出改进信息
\ELSE
    \STATE $G_{best}' \leftarrow G_{best}$
\ENDIF
\RETURN $G_{best}'$
\end{algorithmic}
\end{algorithm}

\textbf{3. 自适应搜索强度}

算法根据局部搜索的成功率统计信息动态调整搜索强度：

\begin{align}
\text{success\_rate} &= \frac{\text{local\_search\_successes}}{\text{local\_search\_attempts}} \label{eq:success_rate} \\
\text{adaptive\_neighbors} &= \begin{cases}
\min(15, \text{current\_neighbors} + 2), & \text{if } \text{success\_rate} > 0.3 \\
\max(5, \text{current\_neighbors} - 1), & \text{if } \text{success\_rate} < 0.1 \\
\text{current\_neighbors}, & \text{otherwise}
\end{cases} \label{eq:adaptive_neighbors}
\end{align}

\subsubsection{局部搜索统计与监控}

算法维护详细的局部搜索统计信息，用于性能分析和参数调优：

\begin{itemize}
\item $\text{local\_search\_attempts}$：总搜索尝试次数
\item $\text{local\_search\_successes}$：总成功次数
\item $\text{local\_search\_spart\_attempts}$：种群搜索尝试次数
\item $\text{local\_search\_spart\_successes}$：种群搜索成功次数
\item $\text{local\_search\_gbest\_attempts}$：全局最优搜索尝试次数
\item $\text{local\_search\_gbest\_successes}$：全局最优搜索成功次数
\end{itemize}

成功率计算：

\begin{align}
\text{overall\_success\_rate} &= \frac{\text{local\_search\_successes}}{\text{local\_search\_attempts}} \times 100\% \label{eq:overall_success_rate} \\
\text{spart\_success\_rate} &= \frac{\text{local\_search\_spart\_successes}}{\text{local\_search\_spart\_attempts}} \times 100\% \label{eq:spart_success_rate} \\
\text{gbest\_success\_rate} &= \frac{\text{local\_search\_gbest\_successes}}{\text{local\_search\_gbest\_attempts}} \times 100\% \label{eq:gbest_success_rate}
\end{align}

\section{算法集成与协调机制}

\subsection{状态感知与策略协调}

ANFDE-IM算法通过状态感知机制协调各个组件的工作：

\begin{enumerate}
\item \textbf{状态检测}：计算景观状态值$\lambda$并确定当前搜索状态
\item \textbf{策略选择}：根据状态选择相应的变异算子
\item \textbf{参数调整}：基于成功经验更新参数分布
\item \textbf{局部优化}：对优质解进行精细搜索
\item \textbf{多样性维护}：通过逃逸机制防止过早收敛
\end{enumerate}

\subsection{计算复杂度分析}

\textbf{参数生成复杂度：}$O(1)$（每个个体）

\textbf{变异操作复杂度：}$O(k)$（节点替换操作）

\textbf{交叉操作复杂度：}$O(k)$（位置交叉）

\textbf{局部搜索复杂度：}$O(k \times \text{max\_neighbors} \times \bar{d})$

\textbf{总体复杂度：}$O(G \times N \times (k + k \times \text{max\_neighbors} \times \bar{d}))$

其中$G$为最大迭代次数，$N$为种群大小，$k$为种子集大小，$\bar{d}$为平均度数。

\subsection{算法特性总结}

ANFDE-IM算法的状态驱动差分进化机制具有以下特性：

\begin{enumerate}
\item \textbf{自适应性}：参数和策略根据搜索状态自动调整
\item \textbf{平衡性}：在探索与开发之间实现动态平衡
\item \textbf{高效性}：通过预计算和缓存机制提高计算效率
\item \textbf{鲁棒性}：多重保障机制确保算法稳定性
\item \textbf{可扩展性}：模块化设计便于扩展和改进
\end{enumerate}

\subsection{交叉策略}

交叉操作是差分进化算法中连接变异与选择的关键环节，其目的在于融合目标个体与变异个体的优势特征，生成具有更高潜在适应度的试验个体。在连续优化问题中，经典的二项式交叉操作通过概率控制机制决定试验个体各维度的来源，其数学表达为：

\begin{equation}
\label{eq:continuous_crossover}
u_{i,j} = \begin{cases}
v_{i,j}, & \text{if } \text{rand}(0,1) \leq CR \text{ or } j = j_{rand} \\
x_{i,j}, & \text{otherwise}
\end{cases}
\end{equation}

其中$u_{i,j}$、$v_{i,j}$和$x_{i,j}$分别表示试验个体、变异个体和目标个体在第$j$维的分量，$CR$为交叉概率，$j_{rand}$为随机选择的维度索引，确保试验个体至少有一个维度来自变异个体。

然而，影响力最大化问题的离散特性使得直接应用上述交叉机制面临挑战。节点集合的交叉不仅需要考虑概率控制，还需处理集合元素的唯一性约束。为此，本研究设计了适用于离散优化的交叉策略。

\subsubsection{基础二项式交叉}

基础交叉策略保持了经典二项式交叉的概率控制思想，将其适配到节点集合操作中。对于目标个体$X_i = \{x_{i,1}, x_{i,2}, \ldots, x_{i,k}\}$和变异个体$M_i = \{m_{i,1}, m_{i,2}, \ldots, m_{i,k}\}$，交叉操作按位置进行：

\begin{equation}
\label{eq:discrete_crossover}
T_{i,j} = 
\begin{cases} 
M_{i,j}, & \text{if } \text{rand}(0,1) \leq CR \text{ or } j = j_{\text{rand}} \\
X_{i,j}, & \text{otherwise}
\end{cases}
\tag{34}
\end{equation}

其中$T_i$为生成的试验个体，$j \in \{1, 2, \ldots, k\}$为位置索引。该操作可能产生重复节点，需要通过修复机制$R(\cdot)$处理：

\begin{equation}
\label{eq:crossover_repair}
T_i = R(\{T_{i,1}, T_{i,2}, \ldots, T_{i,k}\})
\end{equation}

修复函数$R(\cdot)$检测重复节点并用度中心性高于平均水平的未选节点替换，确保试验个体的有效性。

\subsubsection{动态交叉策略}

为进一步提高交叉质量，本研究设计了考虑网络拓扑结构的动态交叉策略。该策略在基础交叉的基础上，引入共同邻居节点的概念，增强解的连通性和影响传播效率。

首先计算目标个体与变异个体的共同邻居集合：

\begin{equation}
\label{eq:common_neighbors}
CN = \bigcup_{v \in X_i \cap M_i} N(v) \setminus (X_i \cup M_i)
\end{equation}

其中$N(v)$表示节点$v$的邻居集合，$CN$为共同邻居节点集合。动态交叉操作在基础交叉的基础上，以一定概率引入共同邻居节点：

\begin{equation}
\label{eq:dynamic_crossover}
T_{i,j} = \begin{cases}
M_{i,j}, & \text{if } \text{rand}(0,1) \leq CR \\
\text{RandomChoice}(CN), & \text{if } \text{rand}(0,1) \leq \alpha \text{ and } CN \neq \emptyset \\
X_{i,j}, & \text{otherwise}
\end{cases}
\end{equation}

其中$\alpha = 0.2$为共同邻居引入概率。这种设计利用了网络的局部结构特性，有助于生成具有更好连通性的种子集合。

\subsection{选择策略}

选择操作决定了种群的进化方向，是算法收敛性和搜索效率的关键保障。差分进化算法采用贪心选择策略，通过比较试验个体与目标个体的适应度值，选择较优者进入下一代。

\subsubsection{贪心选择机制}

对于每个个体$i$，选择操作比较目标个体$X_i$与试验个体$T_i$的适应度值：

\begin{equation}
\label{eq:greedy_selection}
X_i^{(g+1)} = \begin{cases}
T_i, & \text{if } \text{EDV}(T_i) > \text{EDV}(X_i) \\
X_i, & \text{otherwise}
\end{cases}
\end{equation}

其中$\text{EDV}(\cdot)$为期望扩散值函数，$g$表示当前代数。这种贪心策略确保了种群适应度的单调非递减性，为算法收敛提供了理论保证。

\subsubsection{参数成功记录}

当试验个体优于目标个体时，记录对应的成功参数，用于后续的参数自适应更新：

\begin{align}
\text{if } \text{EDV}(T_i) > \text{EDV}(X_i): \quad
\begin{cases}
S_{CR} \leftarrow S_{CR} \cup \{CR_i\} \\
S_F \leftarrow S_F \cup \{F_i\}
\end{cases}
\label{eq:success_parameter_recording}
\end{align}

其中$S_{CR}$和$S_F$分别为成功交叉概率和缩放因子的集合，$CR_i$和$F_i$为个体$i$对应的参数值。这些成功参数将用于下一代的参数分布更新，实现算法的自适应学习。

\subsubsection{精英保留机制}

为防止优质解的丢失，算法维护全局最优个体$G_{best}$：

\begin{equation}
\label{eq:elite_preservation}
G_{best}^{(g+1)} = \arg\max_{X \in P^{(g+1)}} \text{EDV}(X)
\end{equation}

其中$P^{(g+1)}$为第$g+1$代种群。全局最优个体不仅用于变异操作的引导，还为算法提供了收敛性的度量标准。

\begin{algorithm}[H]
\caption{交叉与选择操作}
\label{alg:crossover_selection}
\begin{algorithmic}[1]
\REQUIRE 目标个体$X_i$，变异个体$M_i$，交叉概率$CR$，缩放因子$F$
\ENSURE 下一代个体$X_i^{(g+1)}$，成功参数集合$S_{CR}, S_F$
\STATE // 交叉操作
\STATE $T_i \leftarrow \text{empty\_list}(k)$
\FOR{$j = 1$ to $k$}
    \IF{$\text{rand}(0,1) \leq CR$}
        \STATE $T_i[j] \leftarrow M_i[j]$ // 来自变异个体
    \ELSE
        \STATE $T_i[j] \leftarrow X_i[j]$ // 来自目标个体
    \ENDIF
\ENDFOR
\STATE $T_i \leftarrow R(T_i)$ // 重复节点修复
\STATE
\STATE // 选择操作
\STATE $fitness_{trial} \leftarrow \text{EDV}(T_i)$
\STATE $fitness_{target} \leftarrow \text{EDV}(X_i)$
\IF{$fitness_{trial} > fitness_{target}$}
    \STATE $X_i^{(g+1)} \leftarrow T_i$ // 选择试验个体
    \STATE $S_{CR} \leftarrow S_{CR} \cup \{CR\}$ // 记录成功参数
    \STATE $S_F \leftarrow S_F \cup \{F\}$
\ELSE
    \STATE $X_i^{(g+1)} \leftarrow X_i$ // 保留目标个体
\ENDIF
\RETURN $X_i^{(g+1)}, S_{CR}, S_F$
\end{algorithmic}
\end{algorithm}

交叉与选择操作的有机结合实现了差分进化算法的核心进化机制。交叉操作通过概率控制和结构感知生成多样化的试验个体，选择操作通过贪心策略和参数记录确保种群质量的持续提升。这种设计在保持算法收敛性的同时，为参数自适应提供了有效的反馈机制，增强了算法在复杂优化问题上的适应能力。

\section{精英导向的局部搜索策略}

局部搜索作为元启发式算法的重要组成部分，其核心在于通过邻域结构的精细探索实现解质量的进一步提升。在影响力最大化问题中，局部搜索不仅需要考虑节点替换的组合效应，还需要充分利用网络的拓扑特性和传播动力学特征。本研究设计了一种精英导向的局部搜索策略，通过对全局最优解的深度优化，实现算法性能的显著提升。

\subsection{局部搜索的理论基础}

从优化理论的角度，局部搜索本质上是在解的邻域空间中寻找局部最优解的过程。对于影响力最大化问题，给定种子集$S = \{v_1, v_2, \ldots, v_k\}$，其邻域可定义为通过单节点替换操作可达的解集合：

\begin{equation}
\label{eq:neighborhood_definition}
\mathcal{N}(S) = \{S' : S' = (S \setminus \{v_i\}) \cup \{u\}, v_i \in S, u \in V \setminus S\}
\end{equation}

传统的局部搜索策略通常采用随机或启发式的邻域探索方式，但这种方法在面对大规模网络时往往效率低下。本研究基于影响力传播的内在机理，提出了一种基于局部影响力值（LFV）的智能邻域搜索策略。

\subsection{基于LFV的邻居预计算机制}

为提高局部搜索的效率和质量，算法在初始化阶段对每个节点的高质量邻居进行预计算。这种预计算机制基于节点的局部影响力值，能够有效筛选出具有高传播潜力的候选节点。

对于网络中的任意节点$v$，其局部影响力值定义为：

\begin{equation}
\label{eq:lfv_definition}
\text{LFV}(v) = |N(v)| \times p
\end{equation}

其中$N(v)$表示节点$v$的邻居集合，$p$为传播概率。基于LFV值，每个节点的Top-K高质量邻居计算如下：

\begin{equation}
\label{eq:top_neighbors_computation}
\text{TopNeighbors}(v) = \text{TopK}(N(v), K, \text{LFV})
\end{equation}

其中$K = 20$为预设的邻居数量上限。这种预计算策略的理论依据在于：高LFV值的邻居节点具有更强的传播能力，用其替换低影响力节点更可能产生正向的适应度增益。

\subsection{精英导向的搜索策略}

不同于传统的全种群局部搜索，本研究采用精英导向的搜索策略，专注于对全局最优解的深度优化。这种策略的理论基础源于帕累托原理：在有限的计算资源约束下，将搜索重点集中在最有潜力的解上能够获得更高的投资回报率。

\subsubsection{全局最优解的深度搜索}

每代进化中，算法对当前全局最优解$G_{best}$进行深度局部搜索。搜索过程采用贪心策略，优先替换LFV值较低的节点：

\begin{equation}
\label{eq:node_priority_ordering}
\text{SearchOrder}(G_{best}) = \text{Sort}(G_{best}, \text{key} = \lambda v: \text{LFV}(v))
\end{equation}

对于排序后的每个节点$v_i$，算法从其Top-K邻居中选择替换候选：

\begin{equation}
\label{eq:replacement_candidates}
\text{Candidates}(v_i) = \text{TopNeighbors}(v_i) \setminus G_{best}
\end{equation}

替换操作的适应度增益评估采用贪心准则：

\begin{equation}
\label{eq:greedy_replacement}
v_i^* = \arg\max_{u \in \text{Candidates}(v_i)} \text{EDV}((G_{best} \setminus \{v_i\}) \cup \{u\})
\end{equation}

仅当替换操作产生正向增益时才接受新解：

\begin{equation}
\label{eq:acceptance_criterion}
G_{best}^{new} = \begin{cases}
(G_{best} \setminus \{v_i\}) \cup \{v_i^*\}, & \text{if } \text{EDV}(G_{best}^{new}) > \text{EDV}(G_{best}) \\
G_{best}, & \text{otherwise}
\end{cases}
\end{equation}

\subsubsection{搜索深度的自适应控制}

为平衡搜索质量与计算效率，算法采用自适应的邻居数量控制机制。对于全局最优解的搜索，最大邻居数设置为10，确保充分的邻域探索：

\begin{equation}
\label{eq:adaptive_neighbor_count}
K_{gbest} = \min(10, |\text{TopNeighbors}(v)|)
\end{equation}

这种设计基于以下理论考虑：全局最优解通常位于适应度地形的峰值附近，其邻域结构相对复杂，需要更深入的探索才能发现潜在的改进方向。

\subsection{局部搜索的收敛性分析}

从理论角度分析，基于LFV的局部搜索策略具有良好的收敛性质。设$S^{(t)}$为第$t$次迭代的解，局部搜索过程可表示为：

\begin{equation}
\label{eq:local_search_iteration}
S^{(t+1)} = \arg\max_{S' \in \mathcal{N}_{LFV}(S^{(t)})} \text{EDV}(S')
\end{equation}

其中$\mathcal{N}_{LFV}(S)$为基于LFV排序的邻域。由于贪心选择策略的单调性，有：

\begin{equation}
\label{eq:monotonic_improvement}
\text{EDV}(S^{(t+1)}) \geq \text{EDV}(S^{(t)})
\end{equation}

结合适应度函数的有界性，可证明局部搜索过程在有限步内收敛到局部最优解。

\begin{algorithm}[H]
\caption{精英导向局部搜索}
\label{alg:elite_local_search}
\begin{algorithmic}[1]
\REQUIRE 全局最优解$G_{best}$，最大邻居数$K_{max} = 10$
\ENSURE 优化后的全局最优解$G_{best}^{new}$
\STATE $G_{best}^{new} \leftarrow G_{best}.\text{copy}()$
\STATE $current\_fitness \leftarrow \text{EDV}(G_{best}^{new})$
\STATE $improvement\_found \leftarrow \text{False}$
\STATE
\STATE // 按LFV值升序排序，优先替换低影响力节点
\STATE $sorted\_nodes \leftarrow \text{Sort}(G_{best}^{new}, \text{key} = \lambda v: \text{LFV}(v))$
\STATE
\FOR{$node \in sorted\_nodes$}
    \STATE // 获取预计算的Top-K高LFV邻居
    \STATE $candidates \leftarrow \text{TopNeighbors}[node][:K_{max}]$
    \STATE
    \FOR{$neighbor \in candidates$}
        \IF{$neighbor \in G_{best}^{new}$}
            \STATE \textbf{continue} // 跳过已存在的节点
        \ENDIF
        \STATE
        \STATE // 生成候选解
        \STATE $candidate\_solution \leftarrow (G_{best}^{new} \setminus \{node\}) \cup \{neighbor\}$
        \STATE $candidate\_fitness \leftarrow \text{EDV}(candidate\_solution)$
        \STATE
        \STATE // 贪心接受改进解
        \IF{$candidate\_fitness > current\_fitness$}
            \STATE $G_{best}^{new} \leftarrow candidate\_solution$
            \STATE $current\_fitness \leftarrow candidate\_fitness$
            \STATE $improvement\_found \leftarrow \text{True}$
            \STATE \textbf{break} // 找到改进即跳出当前节点的搜索
        \ENDIF
    \ENDFOR
    \STATE
    \IF{$improvement\_found$}
        \STATE \textbf{break} // 找到改进即结束整个搜索过程
    \ENDIF
\ENDFOR
\STATE
\STATE // 更新搜索统计信息
\IF{$improvement\_found$}
    \STATE 记录成功搜索
    \STATE 输出改进信息：$G_{best} \rightarrow G_{best}^{new}$
\ENDIF
\STATE
\RETURN $G_{best}^{new}$
\end{algorithmic}
\end{algorithm}

\subsection{计算复杂度与效率分析}

精英导向局部搜索的时间复杂度主要由邻域评估决定。对于种子集大小为$k$的全局最优解，最坏情况下的时间复杂度为：

\begin{equation}
\label{eq:local_search_complexity}
T_{local} = O(k \times K_{max} \times T_{eval})
\end{equation}

其中$T_{eval}$为单次适应度评估的时间复杂度。通过预计算机制和早停策略，实际计算复杂度通常远低于理论上界。

空间复杂度主要由邻居预计算产生：

\begin{equation}
\label{eq:space_complexity}
S_{local} = O(|V| \times K)
\end{equation}

其中$|V|$为网络节点数，$K = 20$为预计算邻居数量。这种空间开销在现代计算环境下是可接受的，且能够显著提升搜索效率。

精英导向的局部搜索策略通过理论指导的邻域设计和自适应的搜索控制，实现了搜索质量与计算效率的良好平衡。该策略不仅能够有效提升全局最优解的质量，还为算法的整体性能提供了重要保障。

\end{document}
