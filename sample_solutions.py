from LHS import *


#==================1：基于LHS采样解空间===============
def sample_lhs(G, k, SN, bridge_nodes, combined_scores):
    # 在函数内部计算 regions, combined_scores, bridge_nodes
    regions = divide_by_diameter(G) # 将图划分为多个区域
    if not regions:
        print("Error: Failed to divide the graph into regions.")
        return []
    bridge_nodes = bridge_nodes # 桥节点
    combined_scores = combined_scores # 计算每个节点的综合中心性分数

    solutions = [] # 存储生成的解。
    seen_solutions = set() # 跟踪已经生成的解，避免重复。
    region_keys = sorted(regions.keys()) # 对区域的键进行排序，确保区域顺序一致。

    # 生成SN个解 最多尝试 20 次生成解，确保解满足条件（例如长度为 k）
    for _ in range(SN):
        attempt = 0
        while attempt < 20:  # 最大尝试次数
            effective_dims = min(k, len(region_keys)) # effective_dims: 当前采样维度
            lhs_sample = lhs(effective_dims, samples=1)[0]  # 采样结果为一个数组，数组长度为 effective_dims
            solution = set() # 初始化解集合 solution，存储采样的节点。

            for i in range(effective_dims):# 对每个有效区域进行节点采样。
                region = list(regions[region_keys[i]])
                if region:
                    idx = int(lhs_sample[i] * len(region)) # 通过 lhs_sample[i] 确定采样索引 idx，从区域中选择一个节点加入解集合
                    solution.add(region[idx])

            # 如果采样解节点数量不足 k，调用补充逻辑
            if len(solution) < k:
                supplement = strict_supplement_with_validation(
                    G, list(solution), k - len(solution), regions, bridge_nodes, combined_scores
                )
                solution.update(supplement)

            solution_list = sorted(list(map(int, solution)))
            solution_tuple = tuple(solution_list)

            if len(solution_list) == k and solution_tuple not in seen_solutions:
                solutions.append(solution_list)
                seen_solutions.add(solution_tuple)
                break
            attempt += 1

        if len(solutions) < SN and attempt >= 20: #补充随机策略
            solution = set(random.sample(list(G.nodes()),k)) #如果LHS采样失败，则随机采样  # 修改这里
            solution_list = sorted(list(map(int, solution)))
            solution_tuple = tuple(solution_list)
            if solution_tuple not in seen_solutions:
                solutions.append(solution_list)
                seen_solutions.add(solution_tuple)


    return solutions


#==========================2.基于度的采样解空间==========================================
def degree(G, k):
    """
    返回图中度数最高的 k 个节点，确保节点ID为整数。
    """
    degree_sequence = dict(nx.degree(G))  # 获取每个节点的度
    sorted_nodes = sorted(degree_sequence, key=degree_sequence.get, reverse=True)  # 按度从高到低排序节点
    return [int(node) for node in sorted_nodes[:k]]  # 返回度最高的 k 个节点，并确保节点为整数类型


def replace(x, N):
    """
    替换当前解 x 中的一个节点，使用集合 N 中不在 x 中的随机节点，确保节点ID为整数。
    """
    N_not_in_x = [n for n in N if n not in x]  # 获取 N 中不在当前解 x 中的节点
    if not N_not_in_x:
        return None  # 如果没有可以替换的节点，返回 None
    random_index = np.random.randint(0, len(N_not_in_x))  # 随机选择一个节点
    return N_not_in_x[random_index]



def sample_score(G, k, SN):
    """
    基于度中心性和扰动生成初始解，确保所有节点ID都是整数。

    参数：
        G: networkx 图对象
        k: 种子集大小
        SN: 要生成的解的数量，即种群大小
        div: 多样性参数（扰动概率）
        n_jobs: 未使用，为适配其他实现保持一致

    返回：
        包含 SN 个初始解的列表
    """
    N = list(map(int, G.nodes))  # 确保所有节点ID都是整数
    solutions = []  # 用于存储所有解的列表

    for _ in range(SN):  # 循环 SN 次，生成 SN 个解
        top_k = degree(G, k)  # 获取度最高的 k 个节点
        solution = top_k[:]  # 创建一个副本，避免修改原始列表
        for j in range(k):  # 遍历解中的每个节点
            if random.random() > 0.5:  # 50%概率进行扰动
                new_node = replace(solution, N)  # 获取一个不在当前解中的随机节点
                if new_node is not None:
                    solution[j] = new_node  # 用新节点替换当前节点
        solutions.append(solution)  # 将生成的解添加到列表中
    return solutions