import random

"""局部影响下降（LID）策略生成种子集"""
def LID(G, k, lfv_cache):

    # 初始化 N 为一个空列表
    N = []

    # 按照局部影响力降序对节点进行排序
    sorted_nodes = sorted(lfv_cache, key=lfv_cache.get, reverse=True)

    # 控制参数
    eta = 10
    theta = 50

    # 选择节点
    for i in range(1, k + 1):
        # 计算搜索范围上界
        up_bound = eta * i + theta
        if up_bound > len(sorted_nodes):
            up_bound = len(sorted_nodes)

        # 从前 up_bound 个节点中随机选择一个节点
        node = random.choice(sorted_nodes[:up_bound])

        # 将选择的节点添加到 N 中
        N.append(node)

    return N

"""从节点集中选取唯一节点"""
def uniq(nodes):

    return random.choice(list(nodes))