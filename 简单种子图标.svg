<?xml version="1.0" encoding="UTF-8"?>
<svg width="60" height="60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .seed-main { fill: #4caf50; stroke: #2e7d32; stroke-width: 2; }
      .seed-normal { fill: #66bb6a; stroke: #388e3c; stroke-width: 1.5; }
      .seed-small { fill: #81c784; stroke: #4caf50; stroke-width: 1; }
      .sprout { stroke: #4caf50; stroke-width: 1.5; fill: none; }
      .leaf { fill: #66bb6a; stroke: #4caf50; stroke-width: 0.5; }
      .glow { fill: #c8e6c9; opacity: 0.5; }
      .text { font-family: Arial, sans-serif; font-size: 7px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
    </style>
    <filter id="soft-glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 发光背景 -->
  <circle cx="30" cy="30" r="25" class="glow" filter="url(#soft-glow)"/>
  
  <!-- 主种子（中心，最大） -->
  <circle cx="30" cy="30" r="8" class="seed-main"/>
  
  <!-- 发芽 -->
  <g transform="translate(30,30)">
    <path d="M 0,8 Q -2,4 0,0 Q 2,4 0,8" class="sprout"/>
    <ellipse cx="-1.5" cy="3" rx="2" ry="1" class="leaf"/>
    <ellipse cx="1.5" cy="4" rx="1.5" ry="0.8" class="leaf"/>
    <!-- 星标表示最优 -->
    <text x="0" y="-12" style="font-size:6px; text-anchor:middle; fill:#ffd700;">★</text>
  </g>
  
  <!-- 周围种子 -->
  <circle cx="15" cy="20" r="5" class="seed-normal"/>
  <circle cx="45" cy="20" r="5" class="seed-normal"/>
  <circle cx="15" cy="40" r="5" class="seed-normal"/>
  <circle cx="45" cy="40" r="5" class="seed-normal"/>
  
  <!-- 小种子 -->
  <circle cx="30" cy="10" r="3" class="seed-small"/>
  <circle cx="30" cy="50" r="3" class="seed-small"/>
  <circle cx="10" cy="30" r="3" class="seed-small"/>
  <circle cx="50" cy="30" r="3" class="seed-small"/>
  
  <!-- 种子标识 -->
  <g transform="translate(15,20)">
    <text x="0" y="-8" style="font-size:4px; text-anchor:middle; fill:#2e7d32;">S1</text>
  </g>
  <g transform="translate(45,20)">
    <text x="0" y="-8" style="font-size:4px; text-anchor:middle; fill:#2e7d32;">S2</text>
  </g>
  <g transform="translate(15,40)">
    <text x="0" y="12" style="font-size:4px; text-anchor:middle; fill:#2e7d32;">S3</text>
  </g>
  <g transform="translate(45,40)">
    <text x="0" y="12" style="font-size:4px; text-anchor:middle; fill:#2e7d32;">S4</text>
  </g>
  
  <!-- 标题 -->
  <text x="30" y="8" class="text">种子集</text>
</svg>
