<?xml version="1.0" encoding="UTF-8"?>
<svg width="80" height="80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .seed-container { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .seed-main { fill: #4caf50; stroke: #2e7d32; stroke-width: 1.5; }
      .seed-normal { fill: #66bb6a; stroke: #388e3c; stroke-width: 1; }
      .seed-small { fill: #81c784; stroke: #4caf50; stroke-width: 0.8; }
      .connection { stroke: #a5d6a7; stroke-width: 1; opacity: 0.8; }
      .sprout { stroke: #4caf50; stroke-width: 1; fill: none; }
      .leaf { fill: #66bb6a; stroke: #4caf50; stroke-width: 0.5; }
      .text { font-family: Arial, sans-serif; font-size: 8px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
    </style>
    <filter id="glow">
      <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 种子容器 -->
  <circle cx="40" cy="40" r="30" class="seed-container" filter="url(#glow)"/>
  
  <!-- 主要种子（中心） -->
  <circle cx="40" cy="40" r="6" class="seed-main"/>
  
  <!-- 周围种子 -->
  <circle cx="25" cy="30" r="4" class="seed-normal"/>
  <circle cx="55" cy="30" r="4" class="seed-normal"/>
  <circle cx="25" cy="50" r="4" class="seed-normal"/>
  <circle cx="55" cy="50" r="4" class="seed-normal"/>
  
  <!-- 小种子 -->
  <circle cx="40" cy="20" r="3" class="seed-small"/>
  <circle cx="40" cy="60" r="3" class="seed-small"/>
  <circle cx="20" cy="40" r="3" class="seed-small"/>
  <circle cx="60" cy="40" r="3" class="seed-small"/>
  
  <!-- 连接线 -->
  <line x1="40" y1="40" x2="25" y2="30" class="connection"/>
  <line x1="40" y1="40" x2="55" y2="30" class="connection"/>
  <line x1="40" y1="40" x2="25" y2="50" class="connection"/>
  <line x1="40" y1="40" x2="55" y2="50" class="connection"/>
  <line x1="40" y1="40" x2="40" y2="20" class="connection"/>
  <line x1="40" y1="40" x2="40" y2="60" class="connection"/>
  <line x1="40" y1="40" x2="20" y2="40" class="connection"/>
  <line x1="40" y1="40" x2="60" y2="40" class="connection"/>
  
  <!-- 发芽效果 -->
  <g transform="translate(40,40)">
    <path d="M 0,6 Q -1,2 0,0 Q 1,2 0,6" class="sprout"/>
    <ellipse cx="-0.5" cy="2" rx="1.5" ry="0.8" class="leaf"/>
    <ellipse cx="0.5" cy="3" rx="1" ry="0.6" class="leaf"/>
    <text x="0" y="-10" style="font-size:5px; text-anchor:middle; fill:#1b5e20; font-weight:bold;">★</text>
  </g>
  
  <!-- 种子数量 -->
  <g transform="translate(65,15)">
    <circle cx="0" cy="0" r="6" fill="#fff" stroke="#4caf50" stroke-width="1"/>
    <text x="0" y="2" style="font-size:5px; text-anchor:middle; fill:#2e7d32; font-weight:bold;">8</text>
  </g>
  
  <!-- 标题 -->
  <text x="40" y="8" class="text">种子集</text>
</svg>
