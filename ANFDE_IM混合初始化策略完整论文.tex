\documentclass[12pt]{article}
\usepackage[utf8]{inputenc}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{xeCJK}
\usepackage{geometry}
\geometry{a4paper,margin=2.5cm}

\title{ANFDE-IM算法混合初始化策略详细研究}
\author{研究团队}
\date{\today}

\begin{document}

\maketitle

\section{混合初始化策略概述}

ANFDE-IM算法采用混合初始化策略，该策略集成了拉丁超立方采样（Latin Hypercube Sampling, LHS）和基于度中心性的启发式采样，通过四阶段筛选机制生成既具有高质量又保持良好多样性的初始种群。

\section{采样阶段}

\subsection{拉丁超立方采样}

LHS是一种分层采样技术，旨在在高维空间中生成均匀分布的样本。对于影响力最大化问题，网络被建模为$d$维采样空间，每个维度对应一个不同的网络区域。

\textbf{网络区域划分：}
网络$G=(V,E)$基于到直径路径$P$的最短路径距离被划分为$d$个区域$R = \{R_0, R_1, \ldots, R_{d-1}\}$：
\begin{equation}
	R_i = \left\{ v \in V : \min_{u \in P} d(v,u) = i \right\}
\end{equation}

\textbf{LHS采样过程：}
给定样本大小$N$，每个样本表示为$X^{(i)} = [x_1^{(i)}, x_2^{(i)}, \ldots, x_d^{(i)}]$，采样过程包括：
\begin{enumerate}
	\item 对于每个维度$j=1,\ldots,d$，生成$\{1,\ldots,N\}$的随机排列$\pi_j$
	\item 对于每个样本$i=1,\ldots,N$，生成均匀随机变量$U_j^{(i)} \sim \mathrm{Uniform}(0,1)$
	\item 计算归一化采样位置：$z_j^{(i)} = \frac{\pi_j(i) - U_j^{(i)}}{N}$
	\item 将连续位置转换为节点索引：$\mathrm{idx}_j^{(i)} = \left\lfloor z_j^{(i)} \cdot |R_j| \right\rfloor$
	\item 选择对应节点：$v_j^{(i)} = \mathrm{list}(R_j)[\mathrm{idx}_j^{(i)}]$
	\item 构建采样解：$S^{(i)} = \{ v_1^{(i)}, v_2^{(i)}, \ldots, v_d^{(i)} \}$
\end{enumerate}

\subsection{度中心性采样}

度中心性采样首先选择图中度数最高的$k$个节点作为基础解，然后对该解中的每个节点以0.5的概率应用扰动：

\begin{equation}
\text{PerturbedSolution}_i = \begin{cases}
\text{RandomNode}(V \setminus S) & \text{if } \text{Random}() > 0.5 \\
\text{OriginalNode}_i & \text{otherwise}
\end{cases}
\end{equation}

\section{混合初始化策略}

采样完成后，算法通过四阶段筛选机制构建最终的初始种群。

\subsection{阶段0：并行适应度预计算}

为提高计算效率，算法首先并行计算所有候选解的适应度值并建立缓存：

\begin{equation}
\text{FitnessCache} = \{(S, \text{LIE}(S)) : S \in \text{LHSSolutions} \cup \text{ScoreSolutions}\}
\end{equation}

其中LIE（Linear Influence Estimation）定义为：
\begin{equation}
\text{LIE}(S) = \sum_{v \in S} \left( 1 + \sum_{u \in N(v)} \frac{1}{\deg(u)} \right)
\end{equation}

\subsection{阶段1：质量筛选}

质量筛选阶段从LHS解和度中心性解中分别选择质量最高的解：

\begin{equation}
n_{quality} = \lfloor N \times \text{quality\_proportion} \rfloor
\end{equation}

其中$\text{quality\_proportion} = 0.5$，$N$是目标种群大小。

\textbf{LHS质量筛选：}
\begin{equation}
\text{LHSQuality} = \text{TopK}(\text{LHSSolutions}, n_{quality}, \text{LIE})
\end{equation}

\textbf{度中心性质量筛选：}
\begin{equation}
\text{ScoreQuality} = \text{TopK}(\text{ScoreSolutions}, n_{quality}, \text{LIE})
\end{equation}

\subsection{阶段2：解集合并}

将质量筛选后的两类解合并：
\begin{equation}
\text{CombinedSolutions} = \text{LHSQuality} \cup \text{ScoreQuality}
\end{equation}

\subsection{阶段3：多样性筛选}

多样性筛选采用贪心策略，确保种群中任意两个解的Jaccard相似度不超过阈值：

\begin{equation}
\text{Jaccard}(S_1, S_2) = \frac{|S_1 \cap S_2|}{|S_1 \cup S_2|}
\end{equation}

\textbf{多样性筛选准则：}
\begin{equation}
\text{Accept}(S_i) = \begin{cases}
\text{True} & \text{if } \max_{S_j \in \text{Selected}} \text{Jaccard}(S_i, S_j) \leq \theta_{sim} \\
\text{False} & \text{otherwise}
\end{cases}
\end{equation}

其中$\theta_{sim} = 0.8$是相似度阈值。

\subsection{阶段4：种群补充机制}

如果多样性筛选后的种群大小小于目标大小，启动补充机制：

\textbf{补充策略1：剩余解补充}
从未被选中的解中按适应度降序补充：
\begin{equation}
\text{RemainingSolutions} = \text{CombinedSolutions} \setminus \text{SelectedSolutions}
\end{equation}

\textbf{补充策略2：桥节点随机生成}
当剩余解不足时，生成新解：
\begin{equation}
\text{NewSolution} = \text{BridgeSample} \cup \text{NonBridgeSample}
\end{equation}

其中：
\begin{equation}
\text{BridgeSample} = \text{RandomSample}(\text{BridgeNodes}, \min(2, |\text{BridgeNodes}|))
\end{equation}

\begin{equation}
\text{NonBridgeSample} = \text{RandomSample}(V \setminus \text{BridgeNodes}, k-|\text{BridgeSample}|)
\end{equation}

\subsection{阶段5：去重与最终筛选}

最后进行去重处理，确保种群中没有重复解：
\begin{equation}
\text{FinalPopulation} = \text{Unique}(\text{InitialPopulation})[:N]
\end{equation}

\section{混合初始化算法实现}

\begin{algorithm}[H]
\caption{混合初始化策略算法}
\label{alg:hybrid_initialization}
\begin{algorithmic}[1]
\REQUIRE LHS解集$L$，度中心性解集$S$，种群大小$N$，传播概率$p$，质量比例$q=0.5$，相似度阈值$\theta=0.8$，桥节点$B$
\ENSURE 初始种群$P$
\STATE // 阶段0：并行适应度预计算
\STATE $\text{AllSolutions} \leftarrow L \cup S$
\STATE $\text{FitnessCache} \leftarrow \text{ParallelComputeLIE}(\text{AllSolutions}, G, p)$
\STATE 
\STATE // 阶段1：质量筛选
\STATE $n_{quality} \leftarrow \lfloor N \times q \rfloor$
\STATE $L_{quality} \leftarrow \text{QualityFilter}(L, \text{FitnessCache}, n_{quality})$
\STATE $S_{quality} \leftarrow \text{QualityFilter}(S, \text{FitnessCache}, n_{quality})$
\STATE 
\STATE // 阶段2：解集合并
\STATE $\text{Combined} \leftarrow L_{quality} \cup S_{quality}$
\STATE 
\STATE // 阶段3：多样性筛选
\STATE $P \leftarrow []$
\STATE $\text{Excluded} \leftarrow \emptyset$
\FOR{$i = 0$ to $|\text{Combined}| - 1$}
    \IF{$i \notin \text{Excluded}$}
        \STATE $P.\text{append}(\text{Combined}[i])$
        \STATE $\text{CurrentSet} \leftarrow \text{Set}(\text{Combined}[i])$
        \FOR{$j = i+1$ to $|\text{Combined}| - 1$}
            \IF{$j \notin \text{Excluded}$}
                \STATE $\text{OtherSet} \leftarrow \text{Set}(\text{Combined}[j])$
                \STATE $\text{Similarity} \leftarrow \frac{|\text{CurrentSet} \cap \text{OtherSet}|}{|\text{CurrentSet} \cup \text{OtherSet}|}$
                \IF{$\text{Similarity} > \theta$}
                    \STATE $\text{Excluded}.\text{add}(j)$
                \ENDIF
            \ENDIF
        \ENDFOR
    \ENDIF
\ENDFOR
\STATE 
\STATE // 阶段4：种群补充
\WHILE{$|P| < N$}
    \STATE $\text{Remaining} \leftarrow \text{Combined} \setminus P$
    \IF{$\text{Remaining} \neq \emptyset$}
        \STATE $\text{Best} \leftarrow \arg\max_{s \in \text{Remaining}} \text{FitnessCache}[s]$
        \STATE $P.\text{append}(\text{Best})$
    \ELSE
        \STATE $\text{BridgeSample} \leftarrow \text{RandomSample}(B, \min(2, |B|))$
        \STATE $\text{NonBridge} \leftarrow V \setminus B$
        \STATE $\text{NonBridgeSample} \leftarrow \text{RandomSample}(\text{NonBridge}, k-|\text{BridgeSample}|)$
        \STATE $\text{NewSol} \leftarrow \text{BridgeSample} \cup \text{NonBridgeSample}$
        \STATE $P.\text{append}(\text{NewSol})$
    \ENDIF
\ENDWHILE
\STATE 
\STATE // 阶段5：去重与最终筛选
\STATE $\text{UniqueSolutions} \leftarrow \text{RemoveDuplicates}(P)$
\RETURN $\text{UniqueSolutions}[:N]$
\end{algorithmic}
\end{algorithm}

\section{算法复杂度分析}

\subsection{时间复杂度}

\textbf{适应度预计算：}$O(SN \times k \times \bar{d})$，其中$SN$是候选解总数，$\bar{d}$是平均度数。

\textbf{质量筛选：}$O(SN \log SN)$，主要是排序操作。

\textbf{多样性筛选：}$O(SN^2 \times k)$，需要计算所有解对之间的相似度。

\textbf{种群补充：}$O(N \times k)$，补充操作相对简单。

总体时间复杂度为$O(SN^2 \times k + SN \times k \times \bar{d})$。

\subsection{空间复杂度}

主要空间开销包括：
- 候选解存储：$O(SN \times k)$
- 适应度缓存：$O(SN)$
- 相似度计算临时空间：$O(k)$

总体空间复杂度为$O(SN \times k)$。

\section{实验验证与参数分析}

\subsection{关键参数设置}

根据大量实验验证，关键参数的最优设置为：
- 质量比例：$q = 0.5$
- 相似度阈值：$\theta_{sim} = 0.8$
- 桥节点比例：$10\%$
- 候选解池大小：$SN = 2N$

\subsection{算法优势分析}

1. \textbf{质量保证：}通过LIE评估和质量筛选，确保初始种群具有较高的平均适应度
2. \textbf{多样性维持：}Jaccard相似度控制有效防止种群过早收敛
3. \textbf{计算效率：}并行适应度计算和缓存机制显著提高效率
4. \textbf{鲁棒性强：}多层次补充机制确保在各种情况下都能生成完整种群

\section{总结}

本文详细介绍了ANFDE-IM算法的混合初始化策略，该策略通过LHS采样和度中心性采样的有机结合，配合四阶段筛选机制，有效解决了影响力最大化问题中初始种群质量与多样性的平衡问题。实验结果表明，该初始化策略能够为后续进化优化过程提供高质量的起始点，显著提升算法的整体性能。

\end{document}
