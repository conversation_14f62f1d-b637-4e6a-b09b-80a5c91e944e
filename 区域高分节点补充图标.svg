<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .region-bg { fill: #fff8e1; stroke: #ffb74d; stroke-width: 2; opacity: 0.5; }
      .region-border { fill: none; stroke: #ffb74d; stroke-width: 1.5; stroke-dasharray: 4,4; }
      .high-score-node { fill: #ffab40; stroke: #ff9800; stroke-width: 1.5; }
      .normal-node { fill: #ffe082; stroke: #ffb74d; stroke-width: 1; }
      .connection { stroke: #ffcc02; stroke-width: 1.5; opacity: 0.7; }
      .star { fill: #fff176; stroke: #ffb74d; stroke-width: 0.8; }
      .text { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; text-anchor: middle; fill: #ff8f00; }
      .small-text { font-family: Arial, sans-serif; font-size: 8px; text-anchor: middle; fill: #ffb74d; }
    </style>
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景区域1 -->
  <ellipse cx="70" cy="80" rx="50" ry="35" class="region-bg"/>
  <ellipse cx="70" cy="80" rx="50" ry="35" class="region-border"/>
  
  <!-- 背景区域2 -->
  <ellipse cx="130" cy="120" rx="45" ry="30" class="region-bg"/>
  <ellipse cx="130" cy="120" rx="45" ry="30" class="region-border"/>
  
  <!-- 背景区域3 -->
  <ellipse cx="100" cy="160" rx="40" ry="25" class="region-bg"/>
  <ellipse cx="100" cy="160" rx="40" ry="25" class="region-border"/>
  
  <!-- 区域1的节点 -->
  <circle cx="50" cy="70" r="6" class="normal-node"/>
  <circle cx="70" cy="85" r="10" class="high-score-node" filter="url(#glow)"/>
  <circle cx="90" cy="75" r="5" class="normal-node"/>
  
  <!-- 区域2的节点 -->
  <circle cx="110" cy="110" r="5" class="normal-node"/>
  <circle cx="130" cy="120" r="9" class="high-score-node" filter="url(#glow)"/>
  <circle cx="150" cy="130" r="6" class="normal-node"/>
  
  <!-- 区域3的节点 -->
  <circle cx="85" cy="155" r="5" class="normal-node"/>
  <circle cx="100" cy="160" r="8" class="high-score-node" filter="url(#glow)"/>
  <circle cx="115" cy="165" r="4" class="normal-node"/>
  
  <!-- 连接线 -->
  <line x1="50" y1="70" x2="70" y2="85" class="connection"/>
  <line x1="70" y1="85" x2="90" y2="75" class="connection"/>
  <line x1="110" y1="110" x2="130" y2="120" class="connection"/>
  <line x1="130" y1="120" x2="150" y2="130" class="connection"/>
  <line x1="85" y1="155" x2="100" y2="160" class="connection"/>
  <line x1="100" y1="160" x2="115" y2="165" class="connection"/>
  <line x1="70" y1="85" x2="110" y2="110" class="connection"/>
  <line x1="130" y1="120" x2="100" y2="160" class="connection"/>
  
  <!-- 星形标记表示高分 -->
  <g transform="translate(70,85)">
    <polygon points="0,-6 1.5,-1.5 6,-1.5 2.5,1 3.5,5.5 0,3 -3.5,5.5 -2.5,1 -6,-1.5 -1.5,-1.5" class="star"/>
  </g>
  <g transform="translate(130,120)">
    <polygon points="0,-5 1.2,-1.2 5,-1.2 2,0.8 2.8,4.5 0,2.5 -2.8,4.5 -2,0.8 -5,-1.2 -1.2,-1.2" class="star"/>
  </g>
  <g transform="translate(100,160)">
    <polygon points="0,-4 1,-1 4,-1 1.5,0.5 2,3.5 0,2 -2,3.5 -1.5,0.5 -4,-1 -1,-1" class="star"/>
  </g>
  
  <!-- 箭头指向高分节点 -->
  <defs>
    <marker id="arrowhead" markerWidth="6" markerHeight="4" refX="5" refY="2" orient="auto">
      <polygon points="0 0, 6 2, 0 4" fill="#ff8f00"/>
    </marker>
  </defs>

  <path d="M 35 55 Q 50 65 62 78" fill="none" stroke="#ff8f00" stroke-width="1.5" marker-end="url(#arrowhead)"/>
  <path d="M 165 105 Q 150 110 138 118" fill="none" stroke="#ff8f00" stroke-width="1.5" marker-end="url(#arrowhead)"/>
  <path d="M 125 175 Q 115 168 108 163" fill="none" stroke="#ff8f00" stroke-width="1.5" marker-end="url(#arrowhead)"/>
  
  <!-- 标题和说明 -->
  <text x="100" y="25" class="text">区域高分节点补充</text>
  <text x="100" y="195" class="small-text">从各区域选择最高评分节点</text>
  
  <!-- 图例 -->
  <g transform="translate(20,20)">
    <circle cx="0" cy="0" r="4" class="high-score-node"/>
    <text x="10" y="3" class="small-text">高分节点</text>
  </g>
  
  <g transform="translate(120,20)">
    <ellipse cx="0" cy="0" rx="15" ry="8" class="region-border"/>
    <text x="20" y="3" class="small-text">区域边界</text>
  </g>
</svg>
