import random
import statistics
from collections import deque
from multiprocessing import Pool


# def mc_influence(G, seed_arr, p, NUM_SIMUS=1000):
#     print(f"\n对种子集开始影响力计算: {seed_arr}")
#     inf = 0
#     num_nodes = G.number_of_nodes()
#     for r in range(NUM_SIMUS):
#         active = set(seed_arr)
#         new_active = set(seed_arr)
#         while new_active:
#             next_active = set()
#             for node in new_active:
#                 neighbors = set(G.neighbors(node))
#                 for neighbor in neighbors:
#                     if neighbor not in active:
#                         if random.random() < p:
#                             next_active.add(neighbor)
#                             active.add(neighbor)
#             new_active = next_active
#         inf += len(active)
#     return inf / NUM_SIMUS



def IC(graph, S, P=0.01, R=10000):
    """
    compute expected influence using MC under IC
        R: number of trials
    """
    sources = set(S)
    inf = 0
    for _ in range(R):
        source_set = sources.copy()
        queue = deque(source_set)
        while True:
            curr_source_set = set()
            while len(queue) != 0:
                curr_node = queue.popleft()
                curr_source_set.update(child for child in graph.neighbors(curr_node) \
                                       if
                                       not (child in source_set) and random.random() <= P)
            if len(curr_source_set) == 0:
                break
            queue.extend(curr_source_set)
            source_set |= curr_source_set
        inf += len(source_set)

    return inf / R


def IC_parallel(params: list):  # 用于并行计算
    """
    并行计算影响力
    使用方法: num->进程数量
    with Pool(num) as p:
        es_inf = statistics.mean(p.map(compute_influence_IC, [[graph, seed_list, p, int(iter_num / num)] for _ in range(num)]))
    :param params: list[graph, seed, p, mc]
    :return: influence
    """
    return IC(params[0], params[1], params[2], params[3])

def mc_influence(G, seed_arr, p, NUM_SIMUS=10000):
    pool = 16
    with Pool(pool) as pooling:
        param = [[G, seed_arr, p, int(NUM_SIMUS / pool)] for _ in range(pool)]
        inf = statistics.mean(pooling.map(IC_parallel, param))
    return inf
