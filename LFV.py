import heapq

"""计算节点 u 的局部影响力 LFV(u)"""
# def get_LFV(G, u, p):
#     """
#     参数说明：
#     G: 网络图对象（边需带权重属性 'activation_prob'，若不设置则使用默认 p）
#     u: 目标节点
#     p: 默认激活概率
#     返回值: 节点 u 的局部影响力值
#     """
#     Lfv_u = 1.0  # 自身影响力
#
#     # 获取 u 的一跳邻居
#     neighbors_u = list(G.neighbors(u))
#     for v in neighbors_u:
#         # 获取 u -> v 的激活概率
#         p_uv = G[u][v].get('activation_prob', p) if G.has_edge(u, v) else p
#
#         # 计算间接影响力项：p_uv * Σ(p_vs)
#         sum_p_vs = 0.0
#         neighbors_v = list(G.neighbors(v))
#         for s in neighbors_v:
#             # 获取 v -> s 的激活概率
#             p_vs = G[v][s].get('activation_prob', p) if G.has_edge(v, s) else p
#             sum_p_vs += p_vs
#
#         # 累加贡献：p_uv + p_uv * sum_p_vs
#         contribution = p_uv + p_uv * sum_p_vs
#         Lfv_u += contribution
#
#     return Lfv_u

def get_LFV(G, u, p):
    LFV = 1
    sum_p_uv = 0

    for v in G.neighbors(u):
        if v != u:
            p_uv = p
            sum_p_vs = 0
            for s in G.neighbors(v):
                if s != u and s != v:
                    p_vs = p
                    sum_p_vs += p_vs
            sum = p_uv * sum_p_vs + p_uv
            sum_p_uv += sum

    LFV = LFV + sum_p_uv

    return LFV


def process_node_lfv(node1, nodes, G, p, k):
    """
    计算节点 node1 与其他节点的 LFV 值，并存储为字典。
    """
    heap = []
    top_k = k + 2

    # 使用列表推导式优化循环
    valid_nodes = [node2 for node2 in nodes if node1 != node2]
    for node2 in valid_nodes:
        lfv_combined = calculate_combined_lfv(G, {node1, node2}, p)
        if len(heap) < top_k:
            heapq.heappush(heap, (lfv_combined, node2))
        elif lfv_combined > heap[0][0]:
            heapq.heapreplace(heap, (lfv_combined, node2))  # 使用 heapreplace 替代 pop+push

    # 不考虑节点对顺序
    result = {(node1, node2): lfv_combined for lfv_combined, node2 in heap}
    result.update({(node2, node1): lfv_combined for lfv_combined, node2 in heap})

    return result


def calculate_combined_lfv(G, S, p):
    """
    计算种子集合 S 的联合 LFV 值。
    """
    combined_lfv = 0
    for node in S:
        combined_lfv += get_LFV(G, node, p)  # 累加种子集合中每个节点的 LFV
    return combined_lfv