<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .set-container { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; stroke-dasharray: 5,3; }
      .node { fill: #66bb6a; stroke: #2e7d32; stroke-width: 1.5; }
      .best-node { fill: #4caf50; stroke: #1b5e20; stroke-width: 2; }
      .connection { stroke: #81c784; stroke-width: 1.5; opacity: 0.8; }
      .set-label { fill: #4caf50; stroke: #2e7d32; stroke-width: 1; }
      .text { font-family: Arial, sans-serif; font-size: 8px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
      .small-text { font-family: Arial, sans-serif; font-size: 6px; text-anchor: middle; fill: #4caf50; }
      .label-text { font-family: Arial, sans-serif; font-size: 7px; font-weight: bold; text-anchor: middle; fill: #fff; }
    </style>
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 集合边界（虚线椭圆表示集合） -->
  <ellipse cx="50" cy="40" rx="40" ry="25" class="set-container" filter="url(#glow)"/>
  
  <!-- 集合标签 -->
  <rect x="15" y="15" width="20" height="12" rx="6" class="set-label"/>
  <text x="25" y="22" class="label-text">SET</text>
  
  <!-- 种子节点（用方形区分普通圆形节点） -->
  <rect x="25" y="25" width="8" height="8" rx="2" class="best-node"/>
  <rect x="45" y="30" width="6" height="6" rx="1.5" class="node"/>
  <rect x="65" y="35" width="6" height="6" rx="1.5" class="node"/>
  <rect x="35" y="45" width="6" height="6" rx="1.5" class="node"/>
  <rect x="55" y="50" width="6" height="6" rx="1.5" class="node"/>
  
  <!-- 连接线显示网络关系 -->
  <line x1="29" y1="29" x2="48" y2="33" class="connection"/>
  <line x1="29" y1="29" x2="38" y2="48" class="connection"/>
  <line x1="48" y1="33" x2="68" y2="38" class="connection"/>
  <line x1="48" y1="33" x2="58" y2="53" class="connection"/>
  <line x1="38" y1="48" x2="58" y2="53" class="connection"/>
  
  <!-- 节点标识 -->
  <text x="29" y="20" style="font-size:5px; text-anchor:middle; fill:#1b5e20; font-weight:bold;">S*</text>
  <text x="48" y="25" style="font-size:4px; text-anchor:middle; fill:#2e7d32;">S1</text>
  <text x="68" y="30" style="font-size:4px; text-anchor:middle; fill:#2e7d32;">S2</text>
  <text x="38" y="60" style="font-size:4px; text-anchor:middle; fill:#2e7d32;">S3</text>
  <text x="58" y="65" style="font-size:4px; text-anchor:middle; fill:#2e7d32;">S4</text>
  
  <!-- 集合大小标识 -->
  <g transform="translate(80,20)">
    <circle cx="0" cy="0" r="8" fill="#ff9800" stroke="#f57c00" stroke-width="1"/>
    <text x="0" y="-2" style="font-size:4px; text-anchor:middle; fill:#fff;">|S|</text>
    <text x="0" y="3" style="font-size:6px; text-anchor:middle; fill:#fff; font-weight:bold;">5</text>
  </g>
  
  <!-- 标题 -->
  <text x="50" y="10" class="text">🎯 种子集合</text>
  <text x="50" y="75" class="small-text">S = {s₁, s₂, s₃, s₄, s₅}</text>
</svg>
