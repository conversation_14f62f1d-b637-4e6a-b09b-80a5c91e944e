<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .node-text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
      .small-text { font-family: Arial, sans-serif; font-size: 8px; text-anchor: middle; }
      .input { fill: #e1f5fe; stroke: #0277bd; stroke-width: 3; }
      .quality { fill: #e8f5e8; stroke: #2e7d32; stroke-width: 3; }
      .diversity { fill: #fce4ec; stroke: #c2185b; stroke-width: 3; }
      .output { fill: #fff3e0; stroke: #ef6c00; stroke-width: 4; }
      .group-bg { fill: #f5f5f5; stroke: #999; stroke-width: 1; stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" class="title">ANFDE-IM多元素质量与多样性筛选机制</text>
  
  <!-- 多元素输入源背景 -->
  <rect x="20" y="60" width="300" height="680" rx="10" class="group-bg"/>
  <text x="170" y="80" class="subtitle">多元素输入源</text>
  
  <!-- LHS解集 -->
  <rect x="40" y="100" width="260" height="80" rx="10" class="input"/>
  <text x="170" y="120" class="node-text">🌐 LHS解集</text>
  <text x="170" y="135" class="small-text">📐 拉丁超立方</text>
  <text x="170" y="150" class="small-text">🎯 空间均匀性</text>
  <text x="170" y="165" class="small-text">💎 SN/2个解</text>
  
  <!-- 启发式解集 -->
  <rect x="40" y="200" width="260" height="80" rx="10" class="input"/>
  <text x="170" y="220" class="node-text">⭐ 启发式解集</text>
  <text x="170" y="235" class="small-text">🧠 度中心性</text>
  <text x="170" y="250" class="small-text">🎯 质量导向</text>
  <text x="170" y="265" class="small-text">⚡ SN/2个解</text>
  
  <!-- 桥节点信息 -->
  <rect x="40" y="300" width="260" height="80" rx="10" class="input"/>
  <text x="170" y="320" class="node-text">🔗 桥节点信息</text>
  <text x="170" y="335" class="small-text">📊 介数中心性</text>
  <text x="170" y="350" class="small-text">🌉 网络关键节点</text>
  <text x="170" y="365" class="small-text">📈 Top 10%</text>
  
  <!-- 综合评分 -->
  <rect x="40" y="400" width="260" height="80" rx="10" class="input"/>
  <text x="170" y="420" class="node-text">⚖️ 综合评分</text>
  <text x="170" y="435" class="small-text">📊 0.6×介数+0.4×度</text>
  <text x="170" y="450" class="small-text">📈 MinMax归一化</text>
  <text x="170" y="465" class="small-text">🎯 节点重要性</text>
  
  <!-- 质量筛选维度背景 -->
  <rect x="360" y="60" width="400" height="320" rx="10" class="group-bg"/>
  <text x="560" y="80" class="subtitle">质量筛选维度</text>
  
  <!-- LIE影响力评估 -->
  <rect x="380" y="100" width="170" height="60" rx="10" class="quality"/>
  <text x="465" y="115" class="node-text">📊 LIE影响力评估</text>
  <text x="465" y="130" class="small-text">🔢 二跳影响力</text>
  <text x="465" y="145" class="small-text">⚡ 向量化计算</text>
  
  <!-- 性能排序 -->
  <rect x="570" y="100" width="170" height="60" rx="10" class="quality"/>
  <text x="655" y="115" class="node-text">🏆 性能排序</text>
  <text x="655" y="130" class="small-text">📈 适应度排名</text>
  <text x="655" y="145" class="small-text">🥇 Top 70%选择</text>
  
  <!-- 并行计算 -->
  <rect x="380" y="180" width="170" height="60" rx="10" class="quality"/>
  <text x="465" y="195" class="node-text">⚡ 并行计算</text>
  <text x="465" y="210" class="small-text">🔄 多线程处理</text>
  <text x="465" y="225" class="small-text">📈 批量评估</text>
  
  <!-- 缓存机制 -->
  <rect x="570" y="180" width="170" height="60" rx="10" class="quality"/>
  <text x="655" y="195" class="node-text">💾 缓存机制</text>
  <text x="655" y="210" class="small-text">🗃️ 适应度缓存</text>
  <text x="655" y="225" class="small-text">🔄 重复利用</text>
  
  <!-- 多样性筛选维度背景 -->
  <rect x="360" y="420" width="400" height="320" rx="10" class="group-bg"/>
  <text x="560" y="440" class="subtitle">多样性筛选维度</text>
  
  <!-- Jaccard相似度 -->
  <rect x="380" y="460" width="170" height="60" rx="10" class="diversity"/>
  <text x="465" y="475" class="node-text">🎨 Jaccard相似度</text>
  <text x="465" y="490" class="small-text">📏 集合距离</text>
  <text x="465" y="505" class="small-text">📊 相似度矩阵</text>
  
  <!-- 多样性过滤 -->
  <rect x="570" y="460" width="170" height="60" rx="10" class="diversity"/>
  <text x="655" y="475" class="node-text">🌈 多样性过滤</text>
  <text x="655" y="490" class="small-text">✂️ 相似解剔除</text>
  <text x="655" y="505" class="small-text">🔄 迭代筛选</text>
  
  <!-- 空间分布 -->
  <rect x="380" y="540" width="170" height="60" rx="10" class="diversity"/>
  <text x="465" y="555" class="node-text">🌟 空间分布</text>
  <text x="465" y="570" class="small-text">🗺️ 区域平衡</text>
  <text x="465" y="585" class="small-text">📊 分布均匀性</text>
  
  <!-- 动态调整 -->
  <rect x="570" y="540" width="170" height="60" rx="10" class="diversity"/>
  <text x="655" y="555" class="node-text">🔧 动态调整</text>
  <text x="655" y="570" class="small-text">📈 实时监控</text>
  <text x="655" y="585" class="small-text">🎛️ 参数优化</text>
  
  <!-- 多元素融合输出背景 -->
  <rect x="800" y="60" width="300" height="680" rx="10" class="group-bg"/>
  <text x="950" y="80" class="subtitle">多元素融合输出</text>
  
  <!-- 高质量种群 -->
  <rect x="820" y="150" width="260" height="100" rx="10" class="output"/>
  <text x="950" y="175" class="node-text">✨ 高质量种群</text>
  <text x="950" y="190" class="small-text">🏆 优秀适应度</text>
  <text x="950" y="205" class="small-text">🎯 性能保证</text>
  <text x="950" y="220" class="small-text">📊 质量验证</text>
  <text x="950" y="235" class="small-text">🥇 前70%解</text>
  
  <!-- 高多样性种群 -->
  <rect x="820" y="300" width="260" height="100" rx="10" class="output"/>
  <text x="950" y="325" class="node-text">🌈 高多样性种群</text>
  <text x="950" y="340" class="small-text">🎨 空间分散</text>
  <text x="950" y="355" class="small-text">🔄 结构差异</text>
  <text x="950" y="370" class="small-text">📊 多样性验证</text>
  <text x="950" y="385" class="small-text">🌟 相似度≤0.8</text>
  
  <!-- 平衡种群 -->
  <rect x="820" y="450" width="260" height="100" rx="10" class="output"/>
  <text x="950" y="475" class="node-text">🎯 平衡种群</text>
  <text x="950" y="490" class="small-text">⚖️ 质量+多样性</text>
  <text x="950" y="505" class="small-text">🌟 最优配置</text>
  <text x="950" y="520" class="small-text">🏆 准备进化</text>
  <text x="950" y="535" class="small-text">📊 N个个体</text>
  
  <!-- 处理流程指示 -->
  <rect x="1150" y="200" width="400" height="400" rx="10" class="group-bg"/>
  <text x="1350" y="220" class="subtitle">处理流程说明</text>
  
  <!-- 流程步骤 -->
  <text x="1170" y="250" class="node-text">🔄 处理流程：</text>
  <text x="1170" y="270" class="small-text">1. 多元素输入并行处理</text>
  <text x="1170" y="290" class="small-text">2. 质量维度批量评估</text>
  <text x="1170" y="310" class="small-text">3. 多样性维度筛选过滤</text>
  <text x="1170" y="330" class="small-text">4. 多目标融合优化</text>
  
  <text x="1170" y="360" class="node-text">⚡ 性能优化：</text>
  <text x="1170" y="380" class="small-text">• 向量化计算加速</text>
  <text x="1170" y="400" class="small-text">• 多线程并行处理</text>
  <text x="1170" y="420" class="small-text">• 智能缓存机制</text>
  <text x="1170" y="440" class="small-text">• 动态参数调整</text>
  
  <text x="1170" y="470" class="node-text">🎯 关键参数：</text>
  <text x="1170" y="490" class="small-text">• quality_ratio = 0.7</text>
  <text x="1170" y="510" class="small-text">• sim_threshold = 0.8</text>
  <text x="1170" y="530" class="small-text">• 权重 [0.6, 0.4]</text>
  <text x="1170" y="550" class="small-text">• 桥节点比例 = 10%</text>
  
  <!-- 连接线 -->
  <!-- 输入到质量筛选 -->
  <line x1="300" y1="140" x2="380" y2="130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="240" x2="380" y2="130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="340" x2="380" y2="130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="440" x2="380" y2="130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 质量筛选内部连接 -->
  <line x1="550" y1="130" x2="570" y2="130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="550" y1="210" x2="570" y2="210" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 质量筛选到多样性筛选 -->
  <line x1="560" y1="260" x2="560" y2="420" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 多样性筛选内部连接 -->
  <line x1="550" y1="490" x2="570" y2="490" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="550" y1="570" x2="570" y2="570" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 多样性筛选到输出 -->
  <line x1="740" y1="490" x2="820" y2="200" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="740" y1="570" x2="820" y2="350" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="760" y1="530" x2="820" y2="500" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 数据流标注 -->
  <text x="340" y="120" class="small-text">数据流</text>
  <text x="780" y="300" class="small-text">融合输出</text>
  <text x="580" y="350" class="small-text">处理流水线</text>
</svg>
