from LIE import *  # 导入你的LIE_two_hop函数
import random
from community import community_louvain  # 确保已安装python-louvain库
from functools import wraps
from collections import defaultdict
import threading

# 创建用于LIE_two_hop函数的缓存装饰器
def cache_lie_fitness():
    """
    为LIE_two_hop函数创建缓存装饰器，提高适应度计算效率
    通过缓存相同解的适应度值，避免重复计算
    """
    cache = {}
    lock = threading.Lock()  # 添加线程锁以确保线程安全
    
    @wraps(LIE_two_hop)
    def wrapper(solution, G, p):
        # 将解转换为可哈希的元组形式，并排序以确保相同内容但顺序不同的解被视为相同
        solution_key = tuple(sorted([int(node) for node in solution]))
        
        # 为图和概率创建哈希键（图对象本身不可哈希）
        # 使用图的节点数、边数作为简单特征
        graph_signature = f"{G.number_of_nodes()}_{G.number_of_edges()}"
        key = (solution_key, graph_signature, p)
        
        with lock:  # 使用锁保证线程安全
            if key not in cache:
                # 缓存未命中，计算适应度
                cache[key] = LIE_two_hop(solution, G, p)
            
            # 缓存清理逻辑
            if len(cache) > 50000:  # 如果缓存过大，则清理一半
                keys_to_keep = list(cache.keys())[-25000:]
                new_cache = {k: cache[k] for k in keys_to_keep}
                cache.clear()
                cache.update(new_cache)
                print(f"[缓存优化] 已清理缓存，当前缓存数量：{len(cache)}")
        
        return cache[key]
    
    def clear_cache():
        with lock:
            cache.clear()
            print("适应度计算缓存已清除")
    
    def get_cache_size():
        return len(cache)
    
    # 添加辅助方法
    wrapper.clear_cache = clear_cache
    wrapper.get_cache_size = get_cache_size
    
    return wrapper

# 应用缓存装饰器，创建全局缓存版本的LIE_two_hop函数
cached_LIE_two_hop = cache_lie_fitness()

# 2. 相似度计算 (solution_similarity) - 保持不变
"""计算两个解的相似度（Jaccard 系数）"""
def solution_similarity(sol1, sol2):

    set1 = set(sol1)
    set2 = set(sol2)
    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))
    return intersection / union if union > 0 else 0

# 3. 质量筛选 (quality_filter) - 修改为串行计算,并确保节点ID为整数
# ======================== 修改：quality_filter 函数 ========================
def quality_filter(solutions, fitness_cache, top_n):
    """基于适应度筛选top_n的解决方案"""
    scored = sorted([(fitness_cache[tuple(sol)], sol) for sol in solutions], key=lambda x: -x[0])
    split = int(top_n * 0.7)  # 前70%高质量
    high_quality = [sol for _, sol in scored[:split]]
    diverse_pool = [sol for _, sol in scored[split:]]
    return high_quality + diverse_pool[:top_n - split]

# 4. 多样性筛选 (diversity_filter) - 使用抽样加速 (保持不变)
def diversity_filter(solutions, similarity_threshold, sample_size=10):
    """基于相似度阈值过滤解集，使用抽样加速
    Args:
        solutions: 待过滤的解集合（列表的列表）
        similarity_threshold: 相似度阈值（0-1之间）
        sample_size: 抽样比较的样本数量（默认10）
    Returns:
        过滤后的解集合，保证集合中任意两个解的相似度不超过阈值
    """
    filtered_solutions = []
    for sol in solutions:
        is_similar = False
        # 从已过滤的解中随机抽样（不超过sample_size个）
        sample = random.sample(filtered_solutions, min(len(filtered_solutions), sample_size)) if filtered_solutions else []

        # 与抽样出的解进行相似度比较
        for existing_sol in sample:
            if solution_similarity(sol, existing_sol) > similarity_threshold:
                is_similar = True
                break  # 发现相似解即停止比较

        # 仅保留不相似解
        if not is_similar:
            filtered_solutions.append(sol)
    return filtered_solutions

#
#
# #混合多样性增强策略
# def enhance_initial_diversity(G, initial_population, bridge_nodes, k, p):
#     """混合多样性增强策略（包含社区扰动和反向学习），并保留最佳个体"""
#     new_population = initial_population.copy()
#
#     # 0. 找到原始种群中的最佳个体
#     best_sol = max(initial_population, key=lambda x: LIE_two_hop(x, G, p))
#
#     # 1. 桥接节点注入
#     for _ in range(int(0.3 * len(initial_population))):
#         new_sol = (
#                 random.sample(bridge_nodes, min(3, len(bridge_nodes))) +
#                 random.sample([n for n in G.nodes if n not in bridge_nodes], k - 3)
#         )
#         new_population.append(new_sol)
#
#     # 2. 社区感知扰动
#     try:
#         partition = community_louvain.best_partition(G)
#         communities = defaultdict(list)
#         for node, comm in partition.items():
#             communities[comm].append(node)
#
#         for _ in range(int(0.2 * len(initial_population))):
#             comm_id = random.choice(list(communities.keys()))
#             perturbed = random.sample(communities[comm_id], min(2, len(communities[comm_id])))
#             perturbed += random.sample(list(G.nodes), k - 2)
#             new_population.append(perturbed)
#     except Exception as e:
#         print(f"社区检测失败: {e}, 跳过此步骤")
#
#     # 3. 反向学习
#     if len(new_population) > 0:
#         reversed_sol = [n for n in G.nodes if n not in best_sol][:k]
#         new_population.append(reversed_sol)
#
#     # 4. 去重，并确保最佳个体始终存在
#     unique_solutions = {tuple(sorted(sol)): sol for sol in new_population}
#     unique_solutions[tuple(sorted(best_sol))] = best_sol  # 确保最佳个体始终存在
#
#     # 5. 截断到原始种群大小
#     final_population = list(unique_solutions.values())[:len(initial_population)]
#
#     # 6. 确保最佳个体始终位于种群最前面 (可选，但推荐)
#     if best_sol not in final_population:
#         final_population[-1] = best_sol  # 替换最后一个个体，确保最佳个体存在
#     final_population.sort(key=lambda x: LIE_two_hop(x, G, p), reverse=True) # 让最好的在最前面
#     return final_population
#
