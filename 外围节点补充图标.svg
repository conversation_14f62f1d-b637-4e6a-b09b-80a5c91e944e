<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .main-network { fill: #f3e5f5; stroke: #ba68c8; stroke-width: 1.5; opacity: 0.5; }
      .main-border { fill: none; stroke: #ba68c8; stroke-width: 1.5; }
      .peripheral-node { fill: #ab47bc; stroke: #9c27b0; stroke-width: 1.5; }
      .isolated-node { fill: #ce93d8; stroke: #ba68c8; stroke-width: 1.5; }
      .main-node { fill: #e1bee7; stroke: #ce93d8; stroke-width: 1; }
      .peripheral-connection { stroke: #ab47bc; stroke-width: 1.5; stroke-dasharray: 3,3; }
      .main-connection { stroke: #e1bee7; stroke-width: 1.2; opacity: 0.7; }
      .island-bg { fill: #f8bbd9; stroke: #f48fb1; stroke-width: 1; opacity: 0.4; }
      .text { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; text-anchor: middle; fill: #8e24aa; }
      .small-text { font-family: Arial, sans-serif; font-size: 8px; text-anchor: middle; fill: #ba68c8; }
    </style>
    <filter id="peripheral-glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 主网络区域 -->
  <ellipse cx="100" cy="100" rx="60" ry="50" class="main-network"/>
  <ellipse cx="100" cy="100" rx="60" ry="50" class="main-border"/>
  
  <!-- 主网络内的节点 -->
  <circle cx="80" cy="80" r="4" class="main-node"/>
  <circle cx="100" cy="85" r="4" class="main-node"/>
  <circle cx="120" cy="90" r="4" class="main-node"/>
  <circle cx="85" cy="105" r="4" class="main-node"/>
  <circle cx="105" cy="110" r="4" class="main-node"/>
  <circle cx="115" cy="120" r="4" class="main-node"/>
  <circle cx="90" cy="125" r="4" class="main-node"/>
  
  <!-- 主网络内连接 -->
  <line x1="80" y1="80" x2="100" y2="85" class="main-connection"/>
  <line x1="100" y1="85" x2="120" y2="90" class="main-connection"/>
  <line x1="85" y1="105" x2="105" y2="110" class="main-connection"/>
  <line x1="105" y1="110" x2="115" y2="120" class="main-connection"/>
  <line x1="90" y1="125" x2="105" y2="110" class="main-connection"/>
  <line x1="80" y1="80" x2="85" y2="105" class="main-connection"/>
  <line x1="120" y1="90" x2="115" y2="120" class="main-connection"/>
  
  <!-- 外围孤立节点 -->
  <circle cx="40" cy="50" r="6" class="peripheral-node" filter="url(#peripheral-glow)"/>
  <circle cx="160" cy="60" r="6" class="peripheral-node" filter="url(#peripheral-glow)"/>
  <circle cx="170" cy="140" r="6" class="peripheral-node" filter="url(#peripheral-glow)"/>
  <circle cx="30" cy="160" r="6" class="peripheral-node" filter="url(#peripheral-glow)"/>
  
  <!-- 小连通分量1 -->
  <ellipse cx="45" cy="170" rx="18" ry="12" class="island-bg"/>
  <circle cx="35" cy="170" r="4" class="isolated-node"/>
  <circle cx="45" cy="165" r="4" class="isolated-node"/>
  <circle cx="55" cy="175" r="4" class="isolated-node"/>
  <line x1="35" y1="170" x2="45" y2="165" class="peripheral-connection"/>
  <line x1="45" y1="165" x2="55" y2="175" class="peripheral-connection"/>
  
  <!-- 小连通分量2 -->
  <ellipse cx="160" cy="170" rx="15" ry="10" class="island-bg"/>
  <circle cx="150" cy="170" r="4" class="isolated-node"/>
  <circle cx="165" cy="175" r="4" class="isolated-node"/>
  <line x1="150" y1="170" x2="165" y2="175" class="peripheral-connection"/>
  
  <!-- 虚线连接表示潜在连接 -->
  <line x1="40" y1="50" x2="60" y2="70" class="peripheral-connection" opacity="0.5"/>
  <line x1="160" y1="60" x2="140" y2="80" class="peripheral-connection" opacity="0.5"/>
  <line x1="170" y1="140" x2="140" y2="130" class="peripheral-connection" opacity="0.5"/>
  <line x1="30" y1="160" x2="60" y2="140" class="peripheral-connection" opacity="0.5"/>
  
  <!-- 外围节点标识图标 -->
  <g transform="translate(40,50)">
    <!-- 孤岛图标 -->
    <ellipse cx="0" cy="-10" rx="6" ry="3" fill="#ab47bc" opacity="0.4"/>
    <circle cx="-2" cy="-10" r="1" fill="#8e24aa"/>
    <circle cx="2" cy="-10" r="1" fill="#8e24aa"/>
  </g>

  <g transform="translate(160,60)">
    <!-- 边缘图标 -->
    <path d="M -6,-6 Q 0,-9 6,-6" fill="none" stroke="#8e24aa" stroke-width="1.5"/>
    <circle cx="0" cy="-6" r="1.5" fill="#ab47bc"/>
  </g>

  <g transform="translate(170,140)">
    <!-- 分离图标 -->
    <circle cx="0" cy="-8" r="2.5" fill="none" stroke="#8e24aa" stroke-width="1" stroke-dasharray="2,2"/>
    <circle cx="0" cy="-8" r="1" fill="#ab47bc"/>
  </g>

  <g transform="translate(30,160)">
    <!-- 外围图标 -->
    <rect x="-5" y="-10" width="10" height="6" rx="3" fill="none" stroke="#8e24aa" stroke-width="1" stroke-dasharray="1,1"/>
    <circle cx="0" cy="-7" r="1.5" fill="#ab47bc"/>
  </g>
  
  <!-- 箭头指向外围节点 -->
  <defs>
    <marker id="peripheral-arrow" markerWidth="6" markerHeight="4" refX="5" refY="2" orient="auto">
      <polygon points="0 0, 6 2, 0 4" fill="#8e24aa"/>
    </marker>
  </defs>

  <path d="M 25 35 Q 32 42 37 47" fill="none" stroke="#8e24aa" stroke-width="1.5" marker-end="url(#peripheral-arrow)"/>
  <path d="M 175 45 Q 168 52 163 57" fill="none" stroke="#8e24aa" stroke-width="1.5" marker-end="url(#peripheral-arrow)"/>
  <path d="M 180 155 Q 175 148 172 143" fill="none" stroke="#8e24aa" stroke-width="1.5" marker-end="url(#peripheral-arrow)"/>
  <path d="M 20 175 Q 25 168 28 163" fill="none" stroke="#8e24aa" stroke-width="1.5" marker-end="url(#peripheral-arrow)"/>
  
  <!-- 标题和说明 -->
  <text x="100" y="25" class="text">外围节点补充</text>
  <text x="100" y="195" class="small-text">孤立节点和小连通分量</text>
  
  <!-- 图例 -->
  <g transform="translate(15,15)">
    <circle cx="0" cy="0" r="3" class="peripheral-node"/>
    <text x="8" y="3" class="small-text">外围节点</text>
  </g>
  
  <g transform="translate(80,15)">
    <circle cx="0" cy="0" r="3" class="isolated-node"/>
    <text x="8" y="3" class="small-text">小分量</text>
  </g>
  
  <g transform="translate(140,15)">
    <ellipse cx="0" cy="0" rx="10" ry="6" class="main-border"/>
    <text x="15" y="3" class="small-text">主网络</text>
  </g>
  
  <!-- 补充说明 -->
  <text x="100" y="40" class="small-text">发现网络边缘的隐藏价值</text>
</svg>
