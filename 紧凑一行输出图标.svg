<?xml version="1.0" encoding="UTF-8"?>
<svg width="280" height="40" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .stop-sign { fill: #f44336; stroke: #d32f2f; stroke-width: 1; }
      .arrow-flow { stroke: #4caf50; stroke-width: 2; }
      .output-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 1.5; }
      .seed-node { fill: #66bb6a; stroke: #2e7d32; stroke-width: 1; }
      .best-seed { fill: #4caf50; stroke: #1b5e20; stroke-width: 1.5; }
      .connection { stroke: #81c784; stroke-width: 0.8; opacity: 0.8; }
      .text { font-family: Arial, sans-serif; font-size: 8px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
      .small-text { font-family: Arial, sans-serif; font-size: 6px; text-anchor: middle; fill: #666; }
    </style>
    <marker id="compact-arrow" markerWidth="5" markerHeight="3" refX="4" refY="1.5" orient="auto">
      <polygon points="0 0, 5 1.5, 0 3" fill="#4caf50"/>
    </marker>
  </defs>
  
  <!-- 循环结束 -->
  <g transform="translate(25,20)">
    <circle cx="0" cy="0" r="12" fill="none" stroke="#ff8f00" stroke-width="1.5" stroke-dasharray="3,1" opacity="0.6"/>
    <polygon points="0,-5 4,-1 4,1 0,5 -4,1 -4,-1" class="stop-sign"/>
    <text x="0" y="1" style="font-size:3px; text-anchor:middle; fill:#fff; font-weight:bold;">END</text>
    <text x="0" y="-18" class="small-text">循环结束</text>
  </g>
  
  <line x1="40" y1="20" x2="55" y2="20" class="arrow-flow" marker-end="url(#compact-arrow)"/>
  
  <!-- 收敛标识 -->
  <g transform="translate(70,20)">
    <circle cx="0" cy="0" r="8" fill="#4caf50"/>
    <path d="M -3,-1 L -1,2 L 3,-2" fill="none" stroke="#fff" stroke-width="1.5" stroke-linecap="round"/>
    <text x="0" y="-15" class="small-text">收敛</text>
  </g>
  
  <line x1="82" y1="20" x2="97" y2="20" class="arrow-flow" marker-end="url(#compact-arrow)"/>
  
  <!-- 输出种子集 -->
  <g transform="translate(120,20)">
    <rect x="-18" y="-10" width="36" height="20" rx="3" class="output-box"/>
    
    <!-- 种子节点 -->
    <circle cx="-10" cy="-3" r="2.5" class="seed-node"/>
    <circle cx="-2" cy="0" r="3" class="best-seed"/>
    <circle cx="6" cy="-3" r="2.5" class="seed-node"/>
    <circle cx="12" cy="3" r="2" class="seed-node"/>
    
    <!-- 连接线 -->
    <line x1="-10" y1="-3" x2="-2" y2="0" class="connection"/>
    <line x1="-2" y1="0" x2="6" y2="-3" class="connection"/>
    <line x1="-2" y1="0" x2="12" y2="3" class="connection"/>
    
    <!-- 输出标签 -->
    <rect x="-15" y="-8" width="8" height="4" rx="1" fill="#ffd700" stroke="#ff8f00" stroke-width="0.5"/>
    <text x="-11" y="-5" style="font-size:3px; text-anchor:middle; fill:#ff8f00;">OUT</text>
    
    <text x="0" y="-16" class="small-text">种子集输出</text>
  </g>
  
  <line x1="142" y1="20" x2="157" y2="20" class="arrow-flow" marker-end="url(#compact-arrow)"/>
  
  <!-- 最终结果 -->
  <g transform="translate(175,20)">
    <!-- 简化奖杯 -->
    <ellipse cx="0" cy="-2" rx="6" ry="3" fill="#ffd700" stroke="#ff8f00" stroke-width="1"/>
    <rect x="-3" y="1" width="6" height="3" rx="1" fill="#8d6e63"/>
    <text x="0" y="0" style="font-size:3px; text-anchor:middle; fill:#ff8f00;">★</text>
    
    <text x="0" y="-13" class="small-text">最优结果</text>
    <text x="0" y="12" style="font-size:5px; text-anchor:middle; fill:#2e7d32;">95.2%</text>
  </g>
  
  <!-- 性能指标 -->
  <g transform="translate(220,20)">
    <rect x="-25" y="-8" width="50" height="16" rx="2" fill="#f1f8e9" stroke="#8bc34a" stroke-width="1"/>
    <text x="0" y="-3" style="font-size:5px; text-anchor:middle; fill:#2e7d32; font-weight:bold;">算法完成</text>
    <text x="0" y="4" style="font-size:4px; text-anchor:middle; fill:#4caf50;">影响力: 95.2% | 种子: 5个</text>
  </g>
  
  <!-- 顶部流程标识 -->
  <text x="140" y="8" style="font-family:Arial; font-size:7px; text-anchor:middle; fill:#666;">🔄→✅→🏆→📊</text>
  
  <!-- 底部说明 -->
  <text x="140" y="37" class="small-text">ANFDE-IM算法执行流程：循环终止 → 收敛确认 → 种子输出 → 结果评估</text>
</svg>
