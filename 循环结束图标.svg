<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .loop-circle { fill: none; stroke: #ff9800; stroke-width: 3; stroke-dasharray: 8,4; }
      .stop-sign { fill: #f44336; stroke: #d32f2f; stroke-width: 2; }
      .check-mark { fill: #4caf50; stroke: #2e7d32; stroke-width: 2; }
      .convergence-line { stroke: #ff9800; stroke-width: 2; opacity: 0.7; }
      .text { font-family: Arial, sans-serif; font-size: 8px; font-weight: bold; text-anchor: middle; fill: #d32f2f; }
      .small-text { font-family: Arial, sans-serif; font-size: 6px; text-anchor: middle; fill: #ff9800; }
    </style>
    <filter id="stop-glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 循环轨迹 -->
  <circle cx="50" cy="50" r="35" class="loop-circle"/>
  
  <!-- 收敛指示线 -->
  <path d="M 20 50 Q 35 35 50 50" class="convergence-line"/>
  <path d="M 80 50 Q 65 35 50 50" class="convergence-line"/>
  <path d="M 50 20 Q 35 35 50 50" class="convergence-line"/>
  <path d="M 50 80 Q 65 65 50 50" class="convergence-line"/>
  
  <!-- 停止标志 -->
  <polygon points="50,35 60,45 60,55 50,65 40,55 40,45" class="stop-sign" filter="url(#stop-glow)"/>
  
  <!-- 停止文字 -->
  <text x="50" y="53" class="text">STOP</text>
  
  <!-- 收敛成功标识 -->
  <g transform="translate(70,30)">
    <circle cx="0" cy="0" r="8" class="check-mark"/>
    <path d="M -3,-1 L -1,2 L 4,-3" fill="none" stroke="#fff" stroke-width="1.5" stroke-linecap="round"/>
  </g>
  
  <!-- 迭代计数 -->
  <g transform="translate(30,30)">
    <rect x="0" y="0" width="16" height="10" rx="2" fill="#fff" stroke="#ff9800" stroke-width="1"/>
    <text x="8" y="7" style="font-size:5px; text-anchor:middle; fill:#ff9800;">Gen:N</text>
  </g>
  
  <!-- 标题 -->
  <text x="50" y="15" style="font-family:Arial; font-size:9px; font-weight:bold; text-anchor:middle; fill:#d32f2f;">循环结束</text>
  <text x="50" y="92" class="small-text">收敛条件满足</text>
</svg>
