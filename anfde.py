import os
import threading
from multiprocessing import Pool
import numpy as np
from scipy.stats import cauchy
import networkx as nx  # 导入 networkx
from LIE import LIE_two_hop
from utils import cached_LIE_two_hop  # 导入缓存版本的LIE_two_hop函数
from LFV import *
from LID import *
from concurrent.futures import ThreadPoolExecutor, as_completed
import itertools
import matplotlib
matplotlib.use('TkAgg')
import matplotlib.pyplot as plt
# 强制设置中文字体支持
def setup_chinese_font():
    """强制设置中文字体，确保所有文本正确显示"""
    import matplotlib.font_manager as fm
    import warnings
    warnings.filterwarnings('ignore')  # 忽略字体警告

    # 清除matplotlib字体缓存
    try:
        fm._rebuild()
    except:
        pass

    # 强制设置字体参数
    plt.rcParams.update({
        'font.sans-serif': ['Microsoft YaHei', 'SimHei', 'SimSun', 'DejaVu Sans'],
        'font.family': 'sans-serif',
        'axes.unicode_minus': False,
        'font.size': 10,
        'figure.titlesize': 14,
        'axes.titlesize': 12,
        'axes.labelsize': 10,
        'xtick.labelsize': 9,
        'ytick.labelsize': 9,
        'legend.fontsize': 9
    })

    print(f"[字体设置] 强制使用中文字体配置")
    return True

# 创建英文版图表函数
def create_english_plot(lambda_history, fitness_history, mutation_history, G, k, FEs, p):
    """创建英文版图表，完全避免中文字体问题"""

    # 设置英文字体
    plt.rcParams.update({
        'font.family': 'DejaVu Sans',
        'font.size': 10,
        'axes.unicode_minus': False
    })

    # 确保image文件夹存在
    os.makedirs('image', exist_ok=True)

    num_nodes = G.number_of_nodes()
    num_edges = G.number_of_edges()
    base_filename = f"image/nodes_{num_nodes}_edges_{num_edges}_k{k}_FEs{FEs}_p{p}_ENGLISH"

    # 确保历史数据长度一致
    min_length = min(len(lambda_history), len(fitness_history), len(mutation_history))
    generations = range(min_length)

    # 创建图表
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6), dpi=300)

    # 第一个子图：λ值变化
    ax1.plot(generations, lambda_history[:min_length],
            color='#2E86AB', linewidth=2, marker='o', markersize=4)
    ax1.set_xlabel('Generation')
    ax1.set_ylabel('Lambda Value')
    ax1.set_title('Landscape State Value λ vs Generation', fontweight='bold')
    ax1.grid(True, alpha=0.3)

    # 第二个子图：适应度变化
    ax2.plot(generations, fitness_history[:min_length],
            color='#A23B72', linewidth=2, marker='s', markersize=4)
    ax2.set_xlabel('Generation')
    ax2.set_ylabel('Fitness Value')
    ax2.set_title('Best Fitness vs Generation', fontweight='bold')
    ax2.grid(True, alpha=0.3)

    # 第三个子图：变异算子状态
    state_to_num = {'convergence': 0, 'exploitation': 1, 'exploration': 2, 'escape': 3}
    state_colors = {0: '#F18F01', 1: '#C73E1D', 2: '#2E86AB', 3: '#A23B72'}

    mutation_states_numeric = [state_to_num.get(state, 0) for state in mutation_history[:min_length]]

    if len(mutation_states_numeric) > 0:
        # 绘制连续线条
        ax3.plot(generations, mutation_states_numeric,
                linewidth=2, marker='o', markersize=6, color='#2E86AB', alpha=0.8)

        # 添加彩色状态点
        for gen, state in zip(generations, mutation_states_numeric):
            color = state_colors.get(state, '#000000')
            ax3.scatter(gen, state, color=color, s=50, zorder=3,
                       edgecolors='white', linewidth=1)

    ax3.set_xlabel('Generation')
    ax3.set_ylabel('Mutation Operator State')
    ax3.set_title('Mutation Operator State Changes', fontweight='bold')
    ax3.set_yticks([0, 1, 2, 3])
    ax3.set_yticklabels(['Convergence', 'Exploitation', 'Exploration', 'Escape'])
    ax3.set_ylim(-0.5, 3.5)
    ax3.grid(True, linestyle='--', alpha=0.3)

    plt.tight_layout()

    # 保存多种格式
    formats = []
    for fmt in ['png', 'svg', 'pdf']:
        filename = f"{base_filename}.{fmt}"
        plt.savefig(filename, format=fmt, dpi=300, bbox_inches='tight')
        formats.append(filename)

    plt.close()
    print(f"English charts saved: {', '.join(formats)}")
    return formats

# 初始化字体设置
setup_chinese_font()
import datetime
import logging
import time
from IC import *

class ANFDE:
    def __init__(self, G=None, p=None, bridge_nodes=None, k=None, distance_type='intersection'):  # 新增bridge_nodes参数
        self.population = []  # 初始化为空列表
        self.lambda_history = []
        self.bridge_nodes = bridge_nodes  # 使用外部传入的桥接节点
        self.gen = 0  # 跟踪当前代数
        self.max_gen = 200  # 默认值，在run()中更新
        self.p = p
        self.k = k  # 初始化 self.k

        # 强制转换节点标签为连续整数，并更新 self.N
        self.G = nx.convert_node_labels_to_integers(G, ordering='sorted')  # 添加排序, 确保节点顺序一致
        self.N = list(self.G.nodes())
        self.lfv_cache = {node: self._calculate_LFV(node) for node in self.N}  # 使用转换后的节点ID重新计算LFV
        self.distance_type = distance_type  # 定义属性
        self.FEs = 0  # 初始化 FEs
        self.fitness_history = []  # 初始化适应度历史记录
        self.lambda_history = []  # 初始化 lambda 历史记录
        self.mutation_history = []  # 新增：记录每代使用的变异算子
        
        # 添加局部搜索统计信息
        self.local_search_attempts = 0
        self.local_search_successes = 0
        self.local_search_spart_attempts = 0
        self.local_search_spart_successes = 0
        self.local_search_gbest_attempts = 0
        self.local_search_gbest_successes = 0

        # 预计算 LFV 映射
        self.lfv_map = self.precompute_lfv(self.G, self.p, self.k)

        # 预计算 LFV 缓存，用于单节点 LFV 的计算
        self.lfv_cache = {node: self._calculate_LFV(node) for node in self.N}

        self.distance_type = 'jinyong'  # 新增选项

        # ============================逃逸变异变量============================
        self.escape_candidate_pool = []  # 逃逸候选解池（原local_optima_pool）
        self.n_best = 30  # 用于逃逸优化的最优个体数量（原n_best）
        self.max_pool_size = 20  # 逃逸候选解池的最大大小（原max_pool_size）

        #==========================修改状态判断新增的变量========================
        # 新增: 停滞计数器和阈值
        self.stagnation_counter = 0  # 用于记录连续停滞的代数

        self.last_fitness = float('-inf')

        self.fitness_cache = {}  # 添加缓存字典
        # TODO: 本修改新增
        self.top_neighbors = {}  # 初始化一个空字典，用于存储每个节点的 Top 20 高优邻居
        for node in self.N:  # 遍历图中所有节点，假定 self.N 是图中所有节点的集合
            neighbors = sorted(self.G.neighbors(node),  # 获取当前节点的所有邻居，并按照某个指标排序
                               key=lambda n: self.lfv_cache[n],  # 排序依据是邻居节点 n 的 lfv_cache 值
                               reverse=True)[:20]  # 按降序排序，并截取前 20 个高优邻居
            self.top_neighbors[node] = neighbors  # 将当前节点的 Top 20 邻居存入字典，以节点为键，邻居列表为值

        """===参数相关自适应初始值==="""
        self.mu_CR = 0.5  # 种群的交叉率均值（移除sPart前缀）
        self.mu_F = 0.5  # 种群的缩放因子均值（移除sPart前缀）
        self.c = 0.1
        # 新增参数历史记录
        self.CR_history = []  # 记录CR历史值（移除sPart前缀）
        self.F_history = []  # 记录F历史值（移除sPart前缀）

        # 初始化成功集合
        self.success_CR = []  # 成功CR集合（移除sPart前缀）
        self.success_F = []  # 成功F集合（移除sPart前缀）

        # 预计算每个节点的一跳传播影响力 (F 值)，替换原来的 LFV 缓存
        self.F_cache = {node: self.calculate_F(node) for node in self.N}

    """计算节点的一跳传播影响力"""
    def calculate_F(self, node):
        neighbors = set(self.G.neighbors(node))
        F_value = len(neighbors) * self.p  # F值 = 邻居数量 × 每个邻居的传播概率
        return F_value

    """ (停滞检测逻辑优化)"""
    @property
    def stagnation_threshold(self):
        base_threshold = 3
        return base_threshold + int(self.gen / 50)

    """预计算所有节点的 LFV"""
    """优化版LFV预计算 (保留逃逸变异所需的关键节点对)"""
    def precompute_lfv(self, G, p, k, max_workers=16):

        nodes = list(G.nodes())
        lfv_map = {}

        # 仅计算桥接节点与Top-K节点的组合 (减少计算量)
        bridge_nodes = self.bridge_nodes if self.bridge_nodes else []
        top_nodes = sorted(nodes, key=lambda n: self.lfv_cache[n], reverse=True)[:2 * k]

        # 生成需要计算的节点对
        node_pairs = list(itertools.product(bridge_nodes, top_nodes)) + \
                     list(itertools.combinations(top_nodes, 2))

        # 并行计算关键节点对的LFV
        with Pool(max_workers) as pool:
            results = pool.starmap(
                self._compute_pair_lfv,
                [(u, v, G, p) for u, v in node_pairs]
            )

        # 构建映射字典 (包含逃逸变异所需的所有可能替换对)
        for (u, v), lfv in zip(node_pairs, results):
            lfv_map[(u, v)] = lfv
            lfv_map[(v, u)] = lfv    # 添加反向映射()

        return lfv_map

    """LFV协同计算公式"""
    def _compute_pair_lfv(self, u, v, G, p):
        # 计算u被v替换后的协同增益
        common_neighbors = set(G.neighbors(u)) & set(G.neighbors(v))
        return self.lfv_cache[u] + self.lfv_cache[v] + p * len(common_neighbors)


    """使用 get_LFV 函数计算节点的局部影响力值"""
    def _calculate_LFV(self, node):
        return get_LFV(self.G, node, self.p)



    # TODO: 本修改新增 - 优化版适应度函数（修复缓存键错误）
    def fitness(self, individual):
        # 检查是否有重复节点
        if len(individual) != len(set(individual)):
            # 如果有重复节点，先修复再计算适应度
            individual = self._repair_duplicates(individual)

        key = tuple(sorted(individual))  # 使用排序后的元组作为键

        # 线程安全的缓存访问
        with threading.Lock():
            if key not in self.fitness_cache:
                # ========== 原有计算逻辑 ==========
                self.FEs += 1
                computed_fitness = cached_LIE_two_hop(individual, self.G, self.p)

                # 存入新计算结果
                self.fitness_cache[key] = computed_fitness

                # 使用LRU策略的缓存清理（当缓存超过容量时）
                if len(self.fitness_cache) > 15000:  # 增加缓存容量
                    # 保留最近使用的10000个条目
                    recent_keys = list(self.fitness_cache.keys())[-10000:]
                    new_cache = {k: self.fitness_cache[k] for k in recent_keys}
                    self.fitness_cache = new_cache
                    print(f"[缓存优化] 保留最近{len(new_cache)}个条目")

            return self.fitness_cache[key]

    def fitness_batch(self, individuals):
        """批量计算适应度，减少函数调用开销"""
        results = []
        uncached_individuals = []
        uncached_indices = []

        # 检查缓存
        for i, individual in enumerate(individuals):
            if len(individual) != len(set(individual)):
                individual = self._repair_duplicates(individual)
                individuals[i] = individual

            key = tuple(sorted(individual))
            if key in self.fitness_cache:
                results.append(self.fitness_cache[key])
            else:
                results.append(None)
                uncached_individuals.append(individual)
                uncached_indices.append(i)

        # 批量计算未缓存的适应度
        if uncached_individuals:
            # 使用cached_LIE_two_hop代替LIE_two_hop_batch
            batch_fitness = []
            for ind in uncached_individuals:
                batch_fitness.append(cached_LIE_two_hop(ind, self.G, self.p))
            self.FEs += len(uncached_individuals)

            # 更新缓存和结果
            for i, fitness_val in enumerate(batch_fitness):
                idx = uncached_indices[i]
                key = tuple(sorted(uncached_individuals[i]))
                self.fitness_cache[key] = fitness_val
                results[idx] = fitness_val

        return results

    """基于LFV的快速距离计算"""

    def _weighted_distance_fast(self, set1, set2):
        """基于LFV的距离计算公式 (保留原状态划分逻辑)"""
        # 预先计算常用集合
        intersection = set1 & set2
        union = set1 | set2
        lfv_inter = sum(self.lfv_cache[v] for v in intersection)
        lfv_union = sum(self.lfv_cache[v] for v in union)

        # 对称差异版本
        if self.distance_type == 'symmetric':
            # 使用已计算的集合，避免重复计算
            sym_diff = union - intersection  # 等价于 set1 ^ set2，但利用已计算的结果
            lfv_diff = lfv_union - lfv_inter  # 等价于计算差集的lfv之和，但更快
            return lfv_diff / lfv_union if lfv_union != 0 else 0.0

        # 交集版本
        else:
            return 1 - (lfv_inter / lfv_union) if lfv_union != 0 else 0.0

    def node_set_distance_fast(self, set1, set2):
        """快速计算节点集距离 PDI(S1,S2) = |S1△S2| / |S1∪S2|"""
        # 直接使用集合操作，更高效
        if not set1 or not set2:  # 处理空集情况
            return 1.0 if (set1 or set2) else 0.0
            
        sym_diff = len(set1 ^ set2)  # 对称差集大小
        union = len(set1 | set2)  # 并集大小

        # 去除调试输出以提高性能
        return sym_diff / union

    def _weighted_distance(self, sol1, sol2):
        if self.distance_type == 'node_set':
            # print("执行的是 PDI(S1,S2) = |S1△S2| / |S1∪S2|")
            set1 = set(sol1)
            set2 = set(sol2)
            return self.node_set_distance_fast(set1, set2)
        else:
            # print("执行的是加权 PDI(S1,S2) = Σ(LFV(S1△S2)) / Σ(LFV(S1∪S2))")
            set1 = set(sol1)
            set2 = set(sol2)
            return self._weighted_distance_fast(set1, set2)

    """计算景观状态值 λ - 高度优化版本"""
    def _compute_lambda(self, population):
        print("\n[λ计算] 开始 (高度优化版)")
        pop_size = len(population)
        if pop_size < 2:
            return 0.5

        # 预计算所有个体的适应度值（批量计算）
        fitness_values = self.fitness_batch(population)
        best_idx = np.argmax(fitness_values)

        # 使用向量化距离计算
        distance_matrix = self._compute_distance_matrix_vectorized(population)

        # 向量化计算平均距离
        avg_distances = np.mean(distance_matrix, axis=1)

        # 获取关键距离值
        d_g = avg_distances[best_idx]
        d_max = np.max(avg_distances)
        d_min = np.min(avg_distances)

        # 计算并返回景观状态值 λ
        if d_max == d_min:
            return 0.5
        return (d_g - d_min) / (d_max - d_min)

    def _compute_distance_matrix_vectorized(self, population):
        """向量化计算距离矩阵，使用并行计算加速大型种群"""
        pop_size = len(population)
        distance_matrix = np.zeros((pop_size, pop_size))

        # 将种群转换为集合列表以加速集合操作
        pop_sets = [set(ind) for ind in population]
        
        # 如果种群较大，使用并行计算
        if pop_size > 10:
            # 创建任务列表
            tasks = []
            for i in range(pop_size):
                for j in range(i + 1, pop_size):
                    tasks.append((i, j))
                    
            # 定义并行工作函数
            def compute_distance_pair(pair):
                i, j = pair
                if self.distance_type == 'node_set':
                    # 快速集合距离计算
                    sym_diff_size = len(pop_sets[i] ^ pop_sets[j])
                    union_size = len(pop_sets[i] | pop_sets[j])
                    dist = sym_diff_size / union_size if union_size > 0 else 0.0
                else:
                    # 加权距离计算
                    dist = self._weighted_distance_fast(pop_sets[i], pop_sets[j])
                return i, j, dist
            
            # 使用线程池并行计算
            with ThreadPoolExecutor(max_workers=min(16, os.cpu_count() + 4)) as executor:
                results = list(executor.map(compute_distance_pair, tasks))
                
            # 填充距离矩阵
            for i, j, dist in results:
                distance_matrix[i][j] = dist
                distance_matrix[j][i] = dist
        else:
            # 小种群使用常规计算
            for i in range(pop_size):
                for j in range(i + 1, pop_size):
                    if self.distance_type == 'node_set':
                        # 快速集合距离计算
                        sym_diff_size = len(pop_sets[i] ^ pop_sets[j])
                        union_size = len(pop_sets[i] | pop_sets[j])
                        dist = sym_diff_size / union_size if union_size > 0 else 0.0
                    else:
                        # 加权距离计算
                        dist = self._weighted_distance_fast(pop_sets[i], pop_sets[j])

                    distance_matrix[i][j] = dist
                    distance_matrix[j][i] = dist

        return distance_matrix

    """=========================状态区间划分（两种）=========================="""
    """自适应景观状态评估器 (Adaptive Landscape State Evaluator)第3种"""
    # 根据 λ 值和动态阈值确定当前算法状态。
    def _determine_state(self, lambda_val, initialization=False):
        # 初始阶段强制探索（前10代）
        if self.gen < 10:
            logging.info(f"第{self.gen}代: 强制探索状态")  # 已有日志
            # 新增验证输出
            # print(f"\n[状态强制] 代数={self.gen} < 10，强制进入探索状态 (λ={lambda_val:.2f})")
            return 'exploration'
        # 添加调试信息
        # print(f"\n[状态判断] 代数={self.gen} 开始动态状态判断 (λ={lambda_val:.2f})")

        if not self.population and not initialization:
            print("self.population 为空！")
            return 'exploration'

        current_fitness = self._current_best_fitness()
        # 检查是否满足逃逸条件
        if self._check_escape_condition(lambda_val, current_fitness):
            print(f"\n[逃逸触发类型] 条件触发 | λ={lambda_val:.4f} (接近0) 且停滞计数器 {self.stagnation_counter} ≥ 阈值 {self.stagnation_threshold}")
            # logging.info("触发逃逸条件，进入逃逸状态！")
            return 'escape'

        # 计算动态阈值 Q1 和 Q3
        q1, q3 = self._compute_dynamic_thresholds()

        # 定义状态区间
        state_config = {
            'convergence': (0.0, q1),
            'exploitation': (q1, (q1 + q3) / 2),
            'exploration': ((q1 + q3) / 2, q3),
            'escape': (q3, 1.0)
        }

        # 定义状态优先级
        priority_order = ['escape', 'exploration', 'exploitation', 'convergence']

        # 按照优先级顺序检查状态
        for state in priority_order:
            low, high = state_config[state]
            if low <= lambda_val <= high:
                if state == 'escape':
                    print(f"\n[逃逸触发类型] 区间触发 | λ={lambda_val:.4f} ∈ ({q3:.4f},1.0]")
                return state

        # 如果 λ 值不在任何状态区间内，则默认进入 exploration 状态
        return 'exploration'


    """ 检查是否满足逃逸条件。"""
    """ lambda_val: 景观状态值 λ。
             current_fitness: 当前最佳适应度值
         Returns:如果满足逃逸条件，则返回 True；否则返回 False。
         """
    def _check_escape_condition(self, lambda_val, current_fitness):
        tolerance = 1e-4
        if abs(lambda_val) < tolerance and self.stagnation_counter >= self.stagnation_threshold:
            # === 修改点3：添加逃逸条件触发的详细输出 ===
            print(f"[逃逸条件检测] 满足条件1: λ={lambda_val:.6f} ≈ 0 且停滞 {self.stagnation_counter} ≥ 阈值 {self.stagnation_threshold}")
            self.stagnation_counter = 0
            return True
        if current_fitness <= self.last_fitness:
            # === 修改点4：添加停滞计数器更新提示 ===
            print(f"[逃逸条件检测] 适应度未提升: 当前 {current_fitness} ≤ 前代 {self.last_fitness} | 停滞计数+1 → {self.stagnation_counter + 1}")
            self.stagnation_counter += 1
        else:
            # === 修改点5：添加适应度提升时的重置提示 ===
            print(f"[逃逸条件检测] 适应度提升: 当前 {current_fitness} > 前代 {self.last_fitness} | 停滞计数重置")
            self.stagnation_counter = 0
        return False

    """ 计算动态阈值 Q1 和 Q3。"""
    """修改动态阈值计算方法，在初始阶段使用全历史数据"""
    def _compute_dynamic_thresholds(self):
        # 初始阶段使用全部历史数据
        if self.gen < 10:
            recent_lambdas = self.lambda_history
            # 新增调试信息
            print(f"[阈值计算] 初始阶段(代数={self.gen})，使用全部{len(recent_lambdas)}个历史λ值")
        else:
            window_size = 10
            recent_lambdas = self.lambda_history[-window_size:]
            # 新增调试信息
            print(f"[阈值计算] 正常阶段(代数={self.gen})，使用最近{len(recent_lambdas)}个λ值")

        if len(recent_lambdas) >= 2:
            q1 = np.percentile(recent_lambdas, 25)
            q3 = np.percentile(recent_lambdas, 75)
        else:
            q1, q3 = 0.2, 0.7  # 默认值

        # 在返回前添加输出
        print(f"[阈值结果] Q1={q1:.2f}, Q3={q3:.2f} (基于{len(recent_lambdas)}个λ值)")
        return q1, q3
    """获取当前种群的最佳适应度值。"""
    def _current_best_fitness(self):
        """"""
        """当前种群的最佳适应度值。如果种群为空，则返回负无穷大。"""
        if self.population:
            # 如果种群不为空
            return self.fitness(max(self.population, key=self.fitness))  # 返回种群中适应度值最高的个体的适应度值
        else:
            # 如果种群为空
            print(f"警告：种群为空")
            return float('-inf')  # 返回负无穷大


    """===============生成和动态更新CR/F参数的核心方法============"""
    """生成CR和F参数"""

    def generate_parameters(self):
        # 生成交叉概率 CR (正态分布)
        while True:
            CR = np.random.normal(loc=self.mu_CR, scale=0.1)  # 使用论文建议的标准差 0.3
            if 0 <= CR <= 1:  # 确保 CR 在 [0, 1] 范围内
                break

        # 生成缩放因子 F (柯西分布)
        while True:
            F = cauchy.rvs(loc=self.mu_F, scale=0.1)  # 使用论文建议的尺度参数 0.3
            if 0 <= F <= 1:  # 确保 F 在 [0, 1] 范围内
                break
        # 新增调试输出
        print(f"[参数生成] | 当前均值 mu_CR={self.mu_CR:.3f} → 生成CR={CR:.3f} | mu_F={self.mu_F:.3f} → 生成F={F:.3f}")

        # 在返回前记录生成的参数
        self.CR_history.append(CR)
        self.F_history.append(F)

        return CR, F

    def update_parameters(self):
        """更新 CR 和 F 参数，严格遵循论文公式"""
        # 论文的学习率 c 固定为 0.1


        # 添加更新前的参数值输出
        print(f"\n[参数更新前] | mu_CR={self.mu_CR:.3f} | mu_F={self.mu_F:.3f}")
        print(f"[成功集合] CR: {len(self.success_CR)}条 | F: {len(self.success_F)}条")

        # 更新公式，使用成功经验集合的均值
        if self.success_CR:
            self.mu_CR = (1 - self.c) * self.mu_CR + self.c * np.mean(self.success_CR)
        else:
            print("Warning: success_CR is empty, skipping update for mu_CR.")
        if self.success_F:
            self.mu_F = (1 - self.c) * self.mu_F + self.c * np.mean(self.success_F)
        else:
            print("Warning: success_F is empty, skipping update for mu_F.")

        # 参数边界约束，确保更新后的均值在 [0, 1] 范围内
        self.mu_CR = np.clip(self.mu_CR, 0, 1)
        self.mu_F = np.clip(self.mu_F, 0, 1)

        # 添加更新后的参数值输出
        print(f"[参数更新后] | mu_CR={self.mu_CR:.3f} | mu_F={self.mu_F:.3f}")
        print(f"[更新详情] CR变化: {(self.mu_CR - self.mu_CR):.3f} | F变化: {(self.mu_F - self.mu_F):.3f}")

        # 清空成功集合，准备下一代
        self.success_CR = []
        self.success_F = []

    """动态调整的交叉算子"""

    def dynamic_crossover(self, target, mutant, cr, k):
        """改进的动态交叉操作"""
        trial = target.copy()
        target_set = set(target)
        mutant_set = set(mutant)

        # 计算两个解的共同邻居
        common_neighbors = set()
        for node in target_set & mutant_set:
            common_neighbors.update(self.G.neighbors(node))
        common_neighbors -= (target_set | mutant_set)

        # 自适应交叉
        for i in range(k):
            if random.random() < cr:
                trial[i] = mutant[i]
                # 有机会引入共同邻居节点
                if common_neighbors and random.random() < 0.2:
                    neighbor = random.choice(list(common_neighbors))
                    trial[i] = int(neighbor)
                    common_neighbors.remove(neighbor)

        return trial

    """基础二项式交叉操作"""
    def crossover(self, target, mutant, CR, k):
        # TODO: 基础的二项式交叉
        trial = target.copy()
        for i in range(k):
            if random.random() < CR:
                trial[i] = mutant[i]

        # 检查并修复重复节点
        if len(set(trial)) != len(trial):
            trial = self._repair_duplicates(trial)
        return trial

    # i=irand（避免算法停滞）
    # def crossover(self, target, mutant, CR, k):
    #     trial = target.copy()
    #     i_rand = random.randint(0, k - 1)  # 随机选择一个强制交叉的维度
    #
    #     for i in range(k):
    #         if random.random() < CR or i == i_rand:  # 满足CR或强制条件
    #             trial[i] = mutant[i]
    #
    #     # 检查并修复重复节点（针对组合优化问题）
    #     if len(set(trial)) != len(trial):
    #         trial = self._repair_duplicates(trial)
    #     return trial

    """基础选择操作"""

    def selection(self, target, trial, CR, F, is_sPart=True):

        fitness_trial = self.fitness(trial)
        fitness_target = self.fitness(target)

        if fitness_trial > fitness_target:  # 如果 trial 优于 target
            self.success_CR.append(CR)  # 记录成功的 CR
            self.success_F.append(F)  # 记录成功的 F

            return trial
        return target

    """选择不同个体的索引（使用self.population）- 改进版"""
    def _select_distinct(self, current, num):
        available_indices = [
            i for i, ind in enumerate(self.population)
            if ind != current
        ]

        # 如果可用个体不足，使用重复选择策略
        if len(available_indices) < num:
            print(f"[警告] 种群多样性不足：需要{num}个个体，但只有{len(available_indices)}个可用")
            # 重复选择可用的个体直到满足数量要求
            selected_indices = []
            for i in range(num):
                if available_indices:
                    selected_indices.append(available_indices[i % len(available_indices)])
                else:
                    # 如果连一个可用个体都没有，使用当前个体
                    selected_indices.append(0)  # 选择第一个个体
            return [self.population[i] for i in selected_indices]

        indices = random.sample(available_indices, num)
        return [self.population[i] for i in indices]


    # 变异算子 (根据四种状态进行设计)
    def exploration_mutation(self, individual, k):
        # TODO: 搜索探索状态下的变异算子 DE/rand/2
        X_r1 = random.choice(self.population)
        # 如果种群所有个体都相同，使用 LID 生成所有个体
        if all(ind == self.population[0] for ind in self.population):
            X_r2 = LID(self.G, k, self.lfv_cache)
            X_r3 = LID(self.G, k, self.lfv_cache)
            X_r4 = LID(self.G, k, self.lfv_cache)
            X_r5 = LID(self.G, k, self.lfv_cache)
        else:
            X_r2, X_r3, X_r4, X_r5 = self._select_distinct(X_r1, 4)

        # 计算差集
        difference_set1 = list(set(X_r2) - set(X_r3))
        difference_set2 = list(set(X_r4) - set(X_r5))

        # 计算差集大小
        diff_size1 = len(difference_set1)
        diff_size2 = len(difference_set2)

        F = self.mu_F  # 使用当前的缩放因子

        # 计算替换数量
        num_replacements1 = int(F * diff_size1)
        num_replacements2 = int(F * diff_size2)

        # 总的替换数量, 并限制不超过 k
        total_replacements = num_replacements1 + num_replacements2
        num_replacements = min(total_replacements, k)

        mutant = X_r1.copy()  # 创建突变体副本

        # 创建可替换的节点列表
        combined_difference_set = difference_set1 + difference_set2
        random.shuffle(combined_difference_set)  # 打乱顺序

        # 替换操作
        for i in range(num_replacements):
            if combined_difference_set:  # 确保差集不为空
                replacement_node = combined_difference_set.pop(0)  # 从差集中选择节点
                min_lfv_node_index = min(range(len(mutant)), key=lambda x: self.lfv_cache[mutant[x]])  # 选择LFV最小的节点
                mutant[min_lfv_node_index] = replacement_node  # 替换

            # 如果差集为空，并且种群多样性低，则使用 LID 生成节点
            elif all(ind == self.population[0] for ind in self.population):
                replacement_node = LID(self.G, 1, self.lfv_cache)[0]  # LID 只生成一个节点
                min_lfv_node_index = min(range(len(mutant)), key=lambda x: self.lfv_cache[mutant[x]])  # 选择LFV最小的节点
                mutant[min_lfv_node_index] = replacement_node  # 替换
            else:  # 差集为空，且种群多样性不低，不做处理
                break

        # 检查并修复重复节点
        if len(set(mutant)) != len(mutant):
            mutant = self._repair_duplicates(mutant)

        return mutant

    """开发状态下的变异算子DE/current-to-best/1"""
    def exploitation_mutation(self, individual, k):
        # TODO: 实现开发状态下的变异逻辑，DE/current-to-best/1:
        # 选择当前个体
        X_i = individual
        # 选择当前最优个体
        X_best = max(self.population, key=self.fitness)
        # 选择两个不同的个体
        X_r1, X_r2 = self._select_distinct(X_i, 2)
        # 计算差异集
        difference_set1 = set(X_best) - set(X_i)
        difference_set2 = set(X_r1) - set(X_r2)
        # 计算替换数量
        F = self.mu_F  # 使用当前的缩放因子
        num_replacements1 = int(F * len(difference_set1))
        num_replacements2 = int(F * len(difference_set2))
        # 进行替换操作
        mutant = X_i.copy()
        for _ in range(num_replacements1):
            if difference_set1:
                node_to_replace = random.choice(list(difference_set1))
                # 找到影响力最小的节点进行替换
                min_influence_node = min(mutant, key=lambda n: self.lfv_cache[n])  # 不需要再做转换
                mutant[mutant.index(min_influence_node)] = int(node_to_replace)  # 显式转换为整数
                difference_set1.remove(node_to_replace)

        for _ in range(num_replacements2):
            if difference_set2:
                node_to_replace = random.choice(list(difference_set2))
                # 找到影响力最小的节点进行替换
                min_influence_node = min(mutant, key=lambda n: self.lfv_cache[n])
                mutant[mutant.index(min_influence_node)] = int(node_to_replace)  # 显式转换为整数
                difference_set2.remove(node_to_replace)

        if len(set(mutant)) != len(mutant):
            mutant = self._repair_duplicates(mutant)

        return mutant

    """收敛状态下的变异算子DE/best/1"""
    def convergence_mutation(self, individual, k):
        # TODO: 实现收敛状态下的变异逻辑，DE/best/1
        # 选择当前最优个体
        X_best = max(self.population, key=self.fitness)

        # 选择两个不同的个体
        X_r1, X_r2 = self._select_distinct(X_best, 2)

        # 计算差异集
        difference_set = set(X_r1) - set(X_r2)

        # 计算替换数量
        F = self.mu_F  # 使用当前的缩放因子
        num_replacements = int(F * len(difference_set))

        # 进行替换操作
        mutant = X_best.copy()
        for _ in range(num_replacements):
            if difference_set:
                node_to_replace = random.choice(list(difference_set))
                # 找到影响力最小的节点进行替换
                min_influence_node = min(mutant, key=lambda n: self.lfv_cache[n])
                mutant[mutant.index(min_influence_node)] = int(node_to_replace)  # 显式转换为整数
                difference_set.remove(node_to_replace)

        if len(set(mutant)) != len(mutant):
            mutant = self._repair_duplicates(mutant)

        return mutant

    """===================================基于逃逸候选池的指导变异算子==============================================="""
    """逃逸变异算子：基于逃逸候选池的差异引导变异
        第一阶段：构建逃逸候选池（通过全局搜索优化种群中的优质个体）
        第二阶段：利用候选池中解的差异信息引导变异
        Args:individual: 当前个体（种子集合）
        Returns:mutant: 变异后的新个体（包含差异节点替换）
        """
    # def escape_mutation(self, individual):
    #     print(f"进入逃逸状态，构建逃逸候选池并更新 escape_candidate_pool{len(self.escape_candidate_pool)}")
    #     # 1. 对种群中n_best个个体执行全局优化
    #     sorted_population = sorted(self.population, key=self.fitness, reverse=True)
    #     new_optima = []   # 存储新找到的优化解
    #
    #     for ind in sorted_population[:self.n_best]:
    #         # 调用escape_refinement进行全局优化
    #         local_optimum = self.escape_refinement(ind)
    #         local_optimum_tuple = tuple(sorted(local_optimum))  # 转换为元组便于集合操作
    #
    #         # 避免重复个体加入逃逸候选池
    #         if local_optimum_tuple not in [tuple(sorted(x)) for x in self.escape_candidate_pool]:
    #             new_optima.append(local_optimum)
    #             print(f"    找到新的优化解: {sorted(local_optimum)}")
    #
    #
    #     # 2. 合并新解和旧解，并截断到 max_pool_size
    #     if new_optima:
    #         combined_pool = self.escape_candidate_pool + new_optima
    #         sorted_pool = sorted(combined_pool, key=self.fitness, reverse=True)
    #         self.escape_candidate_pool = sorted_pool[:self.max_pool_size]  # 截断到最大池大小
    #         print(f"escape_candidate_pool 更新后大小: {len(self.escape_candidate_pool)}")
    #
    #     # 3. 如果逃逸候选池为空，直接返回当前个体
    #     if not self.escape_candidate_pool:
    #         print("escape_candidate_pool 为空，直接返回当前个体")
    #         return individual
    #
    #     # 4. 从逃逸候选池中随机选择两个解
    #     x1, x2 = random.sample(self.escape_candidate_pool, 2)
    #
    #     # 5. 计算差分向量 |x1 ⊖ x2|
    #     diff = list(set(x1) - set(x2))  # 差分向量
    #
    #     # 6. 扰动变异：用差异集中的节点替换当前个体中的节点
    #     mutant = individual.copy()
    #     replace_count = min(self.k, len(diff))  # 替换节点数量，不能超过 k
    #     replace_indices = random.sample(range(self.k), replace_count)
    #     for i, index in enumerate(replace_indices):
    #         mutant[index] = diff[i % len(diff)]
    #
    #     return mutant  # 返回变异后的个体
    def escape_mutation(self, individual):
        print(f"进入逃逸状态，构建逃逸候选池并更新 escape_candidate_pool{len(self.escape_candidate_pool)}")
        # 1. 对种群中 n_best 个个体执行全局优化
        sorted_population = sorted(self.population, key=self.fitness, reverse=True)
        new_optima = []  # 存储新找到的优化解

        for ind in sorted_population[:self.n_best]:
            # 调用 escape_refinement 进行全局优化
            local_optimum = self.escape_refinement(ind)

            # 避免重复解加入逃逸候选池
            local_optimum_tuple = tuple(sorted(local_optimum))  # 转换为元组便于集合操作
            if local_optimum_tuple not in [tuple(sorted(x)) for x in self.escape_candidate_pool]:
                new_optima.append(local_optimum)
                print(f"    找到新的优化解: {sorted(local_optimum)}")

        # 2. 合并新解和旧解，并截断到 max_pool_size
        if new_optima:
            combined_pool = self.escape_candidate_pool + new_optima
            sorted_pool = sorted(combined_pool, key=self.fitness, reverse=True)
            self.escape_candidate_pool = sorted_pool[:self.max_pool_size]  # 截断到最大池大小
            print(f"escape_candidate_pool 更新后大小: {len(self.escape_candidate_pool)}")

        # 3. 如果逃逸候选池为空，直接返回当前个体
        if not self.escape_candidate_pool:
            print("escape_candidate_pool 为空，直接返回当前个体")
            return individual

        # 4. 从逃逸候选池中随机选择两个解
        x1, x2 = random.sample(self.escape_candidate_pool, 2)

        # 5. 计算差分向量 |x1 ⊖ x2|
        diff = list(set(x1) - set(x2))  # 差分向量

        # 6. 扰动变异：用差异集中的节点替换当前个体中的节点
        mutant = individual.copy()
        replace_count = min(self.k, len(diff))  # 替换节点数量，不能超过 k
        replace_indices = random.sample(range(self.k), replace_count)
        for i, index in enumerate(replace_indices):
            mutant[index] = diff[i % len(diff)]

        if len(set(mutant)) != len(mutant):
            mutant = self._repair_duplicates(mutant)

        return mutant  # 返回变异后的个体

    # ==多阶段构建逃逸候选池，依次尝试单点变异、双点变异和多点变异(构建逃逸候选池),帮助算法跳出局部最优，探索更广阔的解空间======
    """构建逃逸候选池：通过多种变异策略尝试改进当前解,individual: 需要优化的个体解（节点列表）
    返回优化后的个体解（如果能提高适应度）或原始解（如果无法提高适应度）"""

    # def escape_refinement(self, individual):
    #     # 记录原始解，用于后续比较和可能的回退
    #     original = individual.copy()
    #     original_fitness = self.fitness(original)
    #
    #     # ========================= 候选节点范围限制 ================================
    #     # 获取候选节点：度中心性大于网络平均度的节点
    #     degree_threshold = np.mean([self.G.degree(n) for n in self.N])
    #     candidate_nodes = [
    #         node for node in self.N
    #         if node not in original and self.G.degree(node) > degree_threshold
    #     ]
    #
    #     # ========================= 多点变异优化 ================================
    #     # 按当前个体中 F 值从低到高排序，优先替换 F 最低的节点
    #     sorted_nodes = sorted(original, key=lambda n: self.F_cache[n])  # 按 F 值从低到高排序
    #
    #     # 替换操作：将高度中心性候选节点替换到当前个体中低 F 值节点
    #     improved = original.copy()
    #     for node_to_replace in sorted_nodes:
    #         # 若候选池为空，跳出替换操作
    #         if not candidate_nodes:
    #             break
    #
    #         # 替换最低 F 值节点
    #         replacement_node = candidate_nodes.pop(0)  # 从候选池中选一个节点
    #         improved[improved.index(node_to_replace)] = replacement_node
    #
    #         # 如果优化后的解更优，则立即返回优化后的个体
    #         if self.fitness(improved) > original_fitness:
    #             return improved
    #
    #     # 如果没有找到更优解，返回原始个体
    #     return original
    # def escape_refinement(self, individual):
    #     # 记录原始解，用于后续比较和可能的回退
    #     original = individual.copy()
    #     original_fitness = self.fitness(original)
    #
    #     # ========================= 候选节点范围限制 ================================
    #     # 获取候选节点：度中心性大于网络平均度的节点
    #     degree_threshold = np.mean([self.G.degree(n) for n in self.N])
    #     candidate_nodes = [
    #         node for node in self.N
    #         if node not in original and self.G.degree(node) > degree_threshold
    #     ]
    #
    #     # ========================= 多点变异优化 ================================
    #     # 按当前个体中 F 值从低到高排序，优先替换 F 最低的节点
    #     sorted_nodes = sorted(original, key=lambda n: self.F_cache[n])  # 按 F 值从低到高排序
    #
    #     # 替换操作：将高度中心性候选节点替换到当前个体中低 F 值节点
    #     improved = original.copy()
    #     for node_to_replace in sorted_nodes:
    #         # 若候选池为空，跳出替换操作
    #         if not candidate_nodes:
    #             break
    #
    #         # 替换最低 F 值节点
    #         replacement_node = candidate_nodes.pop(0)  # 从候选池中选一个节点
    #         improved[improved.index(node_to_replace)] = replacement_node
    #
    #     # 修复优化后的解中的重复节点
    #     improved = self._repair_duplicates(improved)
    #
    #     # 比较适应度并返回更优解
    #     improved_fitness = self.fitness(improved)
    #     if improved_fitness > original_fitness:
    #         return improved
    #     else:
    #         return original
    def escape_refinement(self, individual):
        # 记录原始解，用于后续比较和可能的回退
        original = individual.copy()
        original_fitness = self.fitness(original)

        # ========================= 候选节点范围限制 ================================
        # 获取候选节点：度中心性大于网络平均度的节点
        degree_threshold = np.mean([self.G.degree(n) for n in self.N])
        candidate_nodes = [
            node for node in self.N
            if node not in original and self.G.degree(node) > degree_threshold
        ]

        # ========================= 多点变异优化 ================================
        # 按当前个体中 F 值从低到高排序，优先替换 F 最低的节点
        sorted_nodes = sorted(original, key=lambda n: self.F_cache[n])  # 按 F 值从低到高排序

        # 计算要替换的节点数量
        num_replacements = int(self.mu_F * self.k)
        num_replacements = min(num_replacements, len(candidate_nodes), len(sorted_nodes))

        # 替换操作：将高度中心性候选节点替换到当前个体中低 F 值节点
        improved = original.copy()
        replacements_done = 0
        for i in range(len(sorted_nodes)):
            if replacements_done >= num_replacements:
                break

            node_to_replace = sorted_nodes[i]
            # 在候选池中找到一个不在当前个体中的节点
            while candidate_nodes and candidate_nodes[0] in improved:
                candidate_nodes.pop(0)  # 移除已存在的节点

            if candidate_nodes:  # 如果还有候选节点
                replacement_node = candidate_nodes.pop(0)
                improved[improved.index(node_to_replace)] = replacement_node
                replacements_done += 1

        # 修复优化后的解中的重复节点
        improved = self._repair_duplicates(improved)
        # 检查是否有重复节点（以防万一）
        if len(set(improved)) != len(improved):
            print("警告：修复后的解仍然包含重复节点，执行额外修复")
            # 使用更强大的修复函数
            improved = self._repair_duplicates(improved)

        # 比较适应度并返回更优解
        improved_fitness = self.fitness(improved)
        if improved_fitness > original_fitness:
            return improved
        else:
            return original

    def _repair_duplicates(self, individual):
        """
        修复解中的重复节点。
        individual: 当前解（可能含有重复节点）
        返回修复后的解（无重复节点）
        """
        # 将解转换为集合，找到重复节点
        node_counts = {}
        for node in individual:
            node_counts[node] = node_counts.get(node, 0) + 1

        # 找到重复节点和缺失节点
        repeated_nodes = [node for node, count in node_counts.items() if count > 1]
        missing_nodes = [node for node in self.N if node not in individual]

        # 修复重复节点
        for repeated_node in repeated_nodes:
            # 替换重复节点为缺失节点中符合条件的节点
            replacement_node = next(
                node for node in missing_nodes if self.G.degree(node) > np.mean([self.G.degree(n) for n in self.N]))
            individual[individual.index(repeated_node)] = replacement_node
            missing_nodes.remove(replacement_node)

        return individual
    # def _repair_duplicates(self, individual):
    #     """
    #     修复解中的重复节点。
    #     individual: 当前解（可能含有重复节点）
    #     返回修复后的解（无重复节点）
    #     """
    #     # 将解转换为列表，以便修改
    #     fixed_solution = []
    #     used_nodes = set()
    #
    #     # 首先保留所有不重复的节点
    #     for node in individual:
    #         if node not in used_nodes:
    #             fixed_solution.append(node)
    #             used_nodes.add(node)
    #
    #     # 如果长度不足k，从未使用的节点中添加
    #     if len(fixed_solution) < self.k:
    #         # 找出所有未使用的节点
    #         unused_nodes = [node for node in self.N if node not in used_nodes]
    #         # 按照局部影响力值排序
    #         unused_nodes.sort(key=lambda n: self.lfv_cache.get(n, 0), reverse=True)
    #         # 添加足够的节点以达到k
    #         fixed_solution.extend(unused_nodes[:self.k - len(fixed_solution)])
    #
    #     return fixed_solution

    """单点逃逸变异方法：通过替换解中的一个节点来尝试提高解的质量
        基于LFV（局部影响力值）的贪婪单点变异策略， 通过选择能最大化LFV值的替换节点来提高解的质量。
        individual: 需要优化的个体解（节点列表）
        变异后的新解（可能与原解相同，如果没有找到更好的替换节点）"""
    def escape_single_mutation(self, individual):

        # 创建原始解的副本，避免直接修改原始解
        new_position = individual.copy()
        # 将个体转换为集合，便于快速查找节点是否存在
        individual_set = set(individual)

        # 随机选择解中的一个节点作为替换目标
        # 这种随机选择策略有助于增加搜索的多样性
        node_to_replace = random.choice(individual)
        # 初始化最大LFV值为负无穷，用于后续比较
        max_lfv = -1
        # 初始化候选替换节点列表
        candidate_nodes = []

        # 找到所有可以用于替换的有效节点
        # 有效节点需要满足两个条件：
        # 1. 不是被替换的节点本身
        # 2. 不在当前解中（避免重复）
        valid_nodes = [node for node in self.N if node != node_to_replace and node not in individual_set]

        # 遍历所有有效节点，寻找能最大化LFV值的替换节点
        for node in valid_nodes:
            # 获取替换后的LFV值
            # 首先尝试从预计算的LFV映射中获取节点对的LFV值
            # 如果映射中不存在，则使用两个节点的LFV值之和作为近似
            lfv = self.lfv_map.get(
                (node_to_replace, node),
                (self.lfv_cache[node_to_replace] + self.lfv_cache[node])
            )

            # 如果找到更高的LFV值，更新最大值和候选节点列表
            if lfv > max_lfv:
                max_lfv = lfv
                # 重置候选节点列表，只保留当前节点
                candidate_nodes = [node]
            # 如果找到相同的LFV值，将当前节点添加到候选列表
            # 这样可以在多个节点具有相同最大LFV值时随机选择一个
            elif lfv == max_lfv:
                candidate_nodes.append(node)

        # 如果找到了候选替换节点，从中随机选择一个进行替换
        # 随机选择策略有助于增加搜索的多样性，避免陷入局部最优
        if candidate_nodes:
            # 随机选择一个候选节点
            new_node = random.choice(candidate_nodes)
            # 在原始解中找到要替换节点的位置，并用新节点替换
            # 使用int()确保节点ID是整数类型
            new_position[individual.index(node_to_replace)] = int(new_node)

        # 返回变异后的新解
        # 注意：如果没有找到候选节点，返回的解与输入相同
        return new_position

    """双节点变异：通过同时替换两个种子节点，基于 LFV 最大化尝试跳出局部最优。"""
    def escape_double_mutation(self, individual):
        new_position = individual.copy()
        individual_set = set(individual)

        # 随机选择两个节点进行替换
        node1, node2 = random.sample(individual, 2)

        # 找到第一个节点的候选替换节点
        valid_nodes1 = [n for n in self.N if n != node1 and n not in individual_set]
        lfv_values1 = [
            (self.lfv_map.get((node1, n), (self.lfv_cache[node1] + self.lfv_cache[n]) * 0.25), n)
            for n in valid_nodes1
        ]
        max_lfv1 = max(lfv_values1)[0] if lfv_values1 else -1
        candidate_nodes1 = [n for lfv, n in lfv_values1 if lfv == max_lfv1]

        if candidate_nodes1:
            # 排除已选节点，找到第二个候选替换节点
            excluded_nodes = individual_set | set(candidate_nodes1)
            valid_nodes2 = [n for n in self.N if n != node2 and n not in excluded_nodes]
            lfv_values2 = [
                (self.lfv_map.get((node2, n), (self.lfv_cache[node2] + self.lfv_cache[n])), n)
                for n in valid_nodes2
            ]
            max_lfv2 = max(lfv_values2)[0] if lfv_values2 else -1
            candidate_nodes2 = [n for lfv, n in lfv_values2 if lfv == max_lfv2]

            if candidate_nodes2:
                # 随机选择最佳候选节点对进行替换
                new_node1 = random.choice(candidate_nodes1)
                new_node2 = random.choice(candidate_nodes2)
                new_position[individual.index(node1)] = int(new_node1)
                new_position[individual.index(node2)] = int(new_node2)

        return new_position

    """多点变异：基于全网信息替换多个节点来优化个体。
           Args:individual: 当前个体（种子集合）。
           Returns:优化后的个体。"""

    def escape_optimization(self, individual):
        original_ind = individual.copy()
        original_fitness = self.fitness(original_ind)

        # 计算替换节点数 NR = 当前变异概率 F * k
        F = self.mu_F
        NR = min(int(F * self.k), self.k)

        # 替换操作：优先替换当前个体中 F 值较低的节点
        new_ind = original_ind.copy()
        sorted_nodes = sorted(new_ind, key=lambda n: self.F_cache[n])  # 按节点 F 值从低到高排序

        # 获取候选节点：度较大的节点和桥节点
        candidate_nodes = [
            node for node in self.N
            if node not in new_ind and (node in self.bridge_nodes or self.G.degree(node) > np.percentile(
                [self.G.degree(n) for n in self.N], 75))
        ]

        # 将候选节点按 F 值排序，从高到低
        sorted_candidates = sorted(candidate_nodes, key=lambda n: self.F_cache[n], reverse=True)

        # 替换操作: 将高 F 值候选节点替换到当前个体中低 F 值节点
        for i in range(NR):
            if i < len(sorted_nodes) and i < len(sorted_candidates):
                replacement_node = sorted_candidates[i]
                if replacement_node not in new_ind:  # 确保无重复
                    new_ind[new_ind.index(sorted_nodes[i])] = replacement_node

        # 比较替换后的个体与原个体的适应度值，返回提升适应度的个体
        new_fitness = self.fitness(new_ind)
        return new_ind if new_fitness > original_fitness else original_ind

    """=========变异算子的选择执行================="""
    def _select_mutation_spart(self, individual, k, state):

        if state == 'escape':
            # print(f"[变异执行] 当前状态：{state}，执行逃逸变异算子")
            mutant = self.escape_mutation(individual)
            return mutant

        elif state == 'convergence':
            # print(f"[变异执行] 当前状态：{state}，执行收敛变异算子")
            mutant = self.convergence_mutation(individual, k)
            return mutant

        elif state == 'exploitation':
            # print(f"[变异执行] 当前状态：{state}，执行开发变异算子")
            mutant = self.exploitation_mutation(individual, k)
            return mutant

        elif state == 'exploration':
            # print(f"[变异执行] 当前状态：{state}，执行探索变异算子")
            mutant = self.exploration_mutation(individual, k)
            return mutant


    """优化解的高效局部搜索方法"""
    def local_search(self, individual, max_neighbors=5, type_str="general"):
        """
        优化解的高效局部搜索方法
        :param individual: 待优化的个体
        :param max_neighbors: 每个节点考虑的最大邻居数量
        :param type_str: 局部搜索类型("general", "spart", "gbest")，用于分类统计
        :return: 优化后的个体
        """
        self.local_search_attempts += 1
        if type_str == "spart":
            self.local_search_spart_attempts += 1
        elif type_str == "gbest":
            self.local_search_gbest_attempts += 1

        best_ind = individual.copy()
        best_fitness = self.fitness(best_ind)
        found_improvement = False

        # 按节点LFV升序排序，优先替换低影响力节点
        sorted_nodes = sorted(individual, key=lambda n: self.lfv_cache[n])

        for node in sorted_nodes:
            # 获取预存的Top-N高LFV邻居
            neighbors = self.top_neighbors.get(node, [])[:max_neighbors]
            for neighbor in neighbors:
                if neighbor in best_ind:
                    continue

                # 生成新解并评估
                new_ind = [n if n != node else neighbor for n in best_ind]
                new_fitness = self.fitness(new_ind)

                # 贪婪接受更优解
                if new_fitness > best_fitness:
                    best_ind = new_ind
                    best_fitness = new_fitness
                    found_improvement = True
                    break  # 找到改进即跳出，加速搜索

        # 更新成功统计
        if found_improvement:
            self.local_search_successes += 1
            if type_str == "spart":
                self.local_search_spart_successes += 1
            elif type_str == "gbest":
                self.local_search_gbest_successes += 1

        return best_ind
        
    def get_local_search_stats(self):
        """获取局部搜索成功率统计信息"""
        overall_success_rate = 0
        if self.local_search_attempts > 0:
            overall_success_rate = self.local_search_successes / self.local_search_attempts * 100
            
        spart_success_rate = 0
        if self.local_search_spart_attempts > 0:
            spart_success_rate = self.local_search_spart_successes / self.local_search_spart_attempts * 100
            
        gbest_success_rate = 0
        if self.local_search_gbest_attempts > 0:
            gbest_success_rate = self.local_search_gbest_successes / self.local_search_gbest_attempts * 100
            
        return {
            "overall": {
                "attempts": self.local_search_attempts,
                "successes": self.local_search_successes,
                "rate": overall_success_rate
            },
            "spart": {
                "attempts": self.local_search_spart_attempts,
                "successes": self.local_search_spart_successes,
                "rate": spart_success_rate
            },
            "gbest": {
                "attempts": self.local_search_gbest_attempts,
                "successes": self.local_search_gbest_successes,
                "rate": gbest_success_rate
            }
        }

    """ 分段阶梯图 - 支持矢量图格式，同时生成中英文版本:"""
    def plot_results(self, filename=None, save_vector=True):
        print("\n[图表生成] 开始生成中英文版本图表...")

        # 首先生成英文版本（确保无字体问题）
        english_files = create_english_plot(
            self.lambda_history,
            self.fitness_history,
            self.mutation_history,
            self.G, self.k, self.FEs, self.p
        )

        # 然后尝试生成中文版本
        try:
            chinese_files = self._create_chinese_plot(filename, save_vector)
            all_files = english_files + chinese_files
            print(f"[图表生成] 成功生成中英文版本图表")
        except Exception as e:
            print(f"[图表生成] 中文版本生成失败: {e}")
            print(f"[图表生成] 使用英文版本作为备选")
            all_files = english_files

        return all_files

    def _create_chinese_plot(self, filename=None, save_vector=True):
        """生成中文版本图表"""
        # 强制重新设置中文字体
        setup_chinese_font()

        # 确保image文件夹存在
        os.makedirs('image', exist_ok=True)

        num_nodes = self.G.number_of_nodes()
        num_edges = self.G.number_of_edges()

        # 如果没有提供文件名，则使用时间戳创建一个
        if filename is None:
            base_filename = f"image/nodes_{num_nodes}_edges_{num_edges}_k{self.k}_FEs{self.FEs}_p{self.p}_CHINESE"
        else:
            base_filename = "image21/" + filename.rsplit('.', 1)[0] + "_CHINESE"

        # 确保 lambda_history, fitness_history 和 mutation_history 长度一致
        min_length = min(len(self.lambda_history), len(self.fitness_history), len(self.mutation_history))
        generations = range(min_length)

        # 创建高质量图表
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6), dpi=300)

        # 绘制 λ 值
        ax1.plot(generations, self.lambda_history[:min_length],
                color='#2E86AB', linewidth=2, marker='o', markersize=3)
        ax1.set_xlabel('代数', fontsize=12)
        ax1.set_ylabel('λ值', fontsize=12)
        ax1.set_title('景观状态值λ随代数变化', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)

        # 绘制适应度
        ax2.plot(generations, self.fitness_history[:min_length],
                color='#A23B72', linewidth=2, marker='s', markersize=3)
        ax2.set_xlabel('代数', fontsize=12)
        ax2.set_ylabel('适应度', fontsize=12)
        ax2.set_title('最佳适应度随代数变化', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)

        # 绘制变异算子 (改进的连续状态图)
        state_to_num = {
            'convergence': 0,
            'exploitation': 1,
            'exploration': 2,
            'escape': 3
        }

        state_colors = {
            0: '#F18F01',  # convergence - 橙色
            1: '#C73E1D',  # exploitation - 红色
            2: '#2E86AB',  # exploration - 蓝色
            3: '#A23B72'   # escape - 紫色
        }

        # 获取截断后的 mutation_history
        mutation_history_truncated = self.mutation_history[:min_length]

        # 将变异状态转换为数字
        mutation_states_numeric = [state_to_num.get(state, 0) for state in mutation_history_truncated]

        if len(mutation_states_numeric) > 0:
            # 绘制连续的状态线
            ax3.plot(generations, mutation_states_numeric,
                    linewidth=2, marker='o', markersize=6,
                    color='#2E86AB', alpha=0.8, label='状态变化')

            # 为每个状态点添加颜色标记
            for i, (gen, state) in enumerate(zip(generations, mutation_states_numeric)):
                color = state_colors.get(state, '#000000')
                ax3.scatter(gen, state, color=color, s=50, zorder=3,
                           edgecolors='white', linewidth=1)

            # 添加状态区域背景色
            for i in range(4):
                ax3.axhspan(i-0.4, i+0.4, alpha=0.1, color=state_colors[i])

        ax3.set_xlabel('代数', fontsize=12)
        ax3.set_ylabel('变异算子状态', fontsize=12)
        ax3.set_title('变异算子状态变化', fontsize=14, fontweight='bold')
        ax3.set_yticks([0, 1, 2, 3])
        ax3.set_yticklabels(['收敛', '开发', '探索', '逃逸'])
        ax3.set_ylim(-0.5, 3.5)  # 设置y轴范围
        ax3.grid(True, linestyle='--', alpha=0.3)

        plt.tight_layout()

        # 保存图像 - 支持多种格式
        formats = []
        if save_vector:
            # 保存矢量图格式
            svg_filename = f"{base_filename}.svg"
            pdf_filename = f"{base_filename}.pdf"
            plt.savefig(svg_filename, format='svg', dpi=300, bbox_inches='tight')
            plt.savefig(pdf_filename, format='pdf', dpi=300, bbox_inches='tight')
            formats.extend([svg_filename, pdf_filename])
            print(f"中文矢量图已保存: {svg_filename}, {pdf_filename}")

        # 保存高质量PNG
        png_filename = f"{base_filename}.png"
        plt.savefig(png_filename, format='png', dpi=300, bbox_inches='tight')
        formats.append(png_filename)
        print(f"中文位图已保存: {png_filename}")

        plt.close()  # 释放内存
        return formats

    """运行ANFDE算法"""
    def run(self, initial_population,k, g, FEsMaxs,
            lhs_solutions, score_solutions,  # <--接收LHS和评分初始解
            best_individual_initial=None, best_fitness_initial=-1):

        start_time = time.time()  # 记录函数开始时间

        self.fitness_cache = {}  # 清空缓存

        # ================ 初始化种群和参数 ================
        self.population = [ind.copy() for ind in initial_population]
        self.gen = 0   # 初始化当前代数，但不再用于循环控制
        # 初始化 last_fitness
        self.last_fitness = float('-inf')

        # 使用传入的参数初始化全局最优解
        if best_individual_initial is not None and best_fitness_initial > -1:
            Gbest = best_individual_initial.copy()
            Gbest_fitness = best_fitness_initial
            print("进入了使用传入的最佳个体初始化 Gbest 的分支")  # 添加此行
            print(f"使用传入的最佳个体初始化 Gbest: {sorted(Gbest)}, 适应度: {Gbest_fitness:.4f}")
        else:
            Gbest = max(self.population, key=self.fitness)  # 使用种群中最佳个体初始化
            Gbest_fitness = self.fitness(Gbest)
            print("进入了使用种群中最佳个体初始化 Gbest 的分支")  # 添加此行
            print(f"使用种群中最佳个体初始化 Gbest: {sorted(Gbest)}, 适应度: {Gbest_fitness:.4f}")

        # 初始化 Pbest  此处不增加FEs，因为后面会统一计算
        Pbest = self.population.copy()
        Pbest_fitness = [self.fitness(ind) for ind in Pbest]  # 此处不增加FEs，因为后面会统一计算
        print("\n=== 初始化Pbest ===")  # 添加打印信息
        for i, individual in enumerate(Pbest):
            print(f"Pbest {i + 1}: {sorted(individual)}, fitness: {Pbest_fitness[i]:.4f}")


        # ================ 主循环 ================
        while self.FEs < FEsMaxs:  # 同时检查FEs和代数:  # 基于函数评估次数的循环
            if self.gen >=self.max_gen:  # 如果代数超过300，则结束循环
                print(f"达到最大代数{self.max_gen}，结束运行。")
                break
            unique_count = len({tuple(sorted(ind)) for ind in self.population})
            print(f"第 {self.gen + 1} 代 | 函数评估次数: {self.FEs}/{FEsMaxs} | 唯一解数量: {unique_count}/{len(self.population)}")  # 修改：添加代数信息
            self.gen += 1  # 更新当前代数，但不再用于循环控制

            # ================ 1. 景观状态分析 ================
            """λ值"""
            lambda_val = self._compute_lambda(self.population) # compute_lambda内部不涉及fitness计算，所以不更新FEs
            self.lambda_history.append(lambda_val)
            """根据λ确定当前状态"""
            current_state = self._determine_state(lambda_val)  # determine_state不涉及fitness计算，所以不更新FEs

            # ================ 2. 动态参数更新 ================
            self.update_parameters()

            # ================ 3. sPart变异 ================
            new_sPart = []

            # 根据状态选择变异算子 (在循环之前)
            if current_state == 'escape':
                mutation_op = 'escape'
            elif current_state == 'convergence':
                mutation_op = 'convergence'
            elif current_state == 'exploitation':
                mutation_op = 'exploitation'
            else:
                mutation_op = 'exploration'

            self.mutation_history.append(mutation_op)  # 记录这一代使用的变异算子
            print(f"第{self.gen}代 选择的变异算子: {mutation_op}, mutation_history: {self.mutation_history}")
            for ind in self.population:
                # 状态感知选择变异策略
                mutant = self._select_mutation_spart(ind, k, current_state)  # 使用 self.representative_optima 和 self.G_lon

                # 参数生成
                CR, F_val = self.generate_parameters()# generate_parameters不涉及fitness计算，所以不更新FEs
                # 交叉与选择
                trial = self.crossover(ind, mutant, CR, k)  # 交叉操作 # crossover不涉及fitness计算，所以不更新FEs
                new_sPart.append(self.selection(ind, trial, CR, F_val, is_sPart=True)) # selection内部涉及fitness计算，会在fitness函数内部更新FEs



            # ================ 6. 对sPart优质解进行局部搜索 ================
            if len(new_sPart) > 0:
                # 选择前10%的个体并行优化
                num_to_optimize = max(1, int(0.1 * len(new_sPart)))
                # num_to_optimize = 1 #对最有个体

                candidates = new_sPart[:num_to_optimize]  # 已排序

                # 串行局部搜索（避免缓存冲突）
                optimized = []
                for candidate in candidates:
                    try:
                        optimized_candidate = self.local_search(candidate, max_neighbors=8, type_str="spart")
                        optimized.append(optimized_candidate)
                    except Exception as e:
                        print(f"[警告] 局部搜索失败: {e}")
                        optimized.append(candidate)  # 使用原始候选解
                # 替换原个体（仅保留改进解）
                for i in range(len(optimized)):
                    if self.fitness(optimized[i]) > self.fitness(candidates[i]):
                        new_sPart[i] = optimized[i]
                        print(f" 对sPart优质解进行局部搜索：{sorted(candidates[i])} -> {sorted(optimized[i])}")

            # ================ 7. 更新种群 ================
            self.population = new_sPart
            # 检查种群中是否有重复节点的个体
            for i, ind in enumerate(self.population):
                if len(set(ind)) != len(ind):
                    self.population[i] = self._repair_duplicates(ind)


            # ================ 8. 更新 Pbest 和全局最优解 ================
            for i, ind in enumerate(self.population):
                current_fitness = self.fitness(ind) # fitness函数内部会更新FEs
                if current_fitness > Pbest_fitness[i]:
                    Pbest[i] = ind.copy()
                    Pbest_fitness[i] = current_fitness

                # 替换原有Gbest更新代码
                if current_fitness > Gbest_fitness:
                    # 生成候选解
                    candidate = ind.copy()

                    # 深度局部搜索（扩大邻域范围）
                    try:
                        optimized = self.local_search(candidate, max_neighbors=10, type_str="gbest")
                        # 适应度更新
                        optimized_fitness = self.fitness(optimized)
                        if optimized_fitness > Gbest_fitness:
                            Gbest = optimized
                            Gbest_fitness = optimized_fitness
                            print(f"Gbest进行局部搜索改进了: {sorted(candidate)} → {sorted(optimized)}")
                        else:
                            Gbest = candidate.copy()
                            Gbest_fitness = current_fitness
                    except Exception as e:
                        print(f"[警告] Gbest局部搜索失败: {e}")
                        Gbest = candidate.copy()
                        Gbest_fitness = current_fitness
            self.last_fitness = Gbest_fitness  # 更新 last_fitness，用于下一代的停滞判断
            # ================ 9. 记录状态 ================
            self.fitness_history.append(Gbest_fitness) # 记录全局最优fitness
            # ================ 10. 打印每代最优个体 ================
            if 'Gbest' in locals():
                print(f"第 {self.gen} 代 | 函数评估次数: {self.FEs} --- 全局最佳LIE 值: {Gbest_fitness:.4f}")
                # print(f"    全局最佳个体: {sorted(Gbest)}") # 可选打印
            else:
                print(f"第 {self.gen} 代 | 函数评估次数: {self.FEs} --- (尚未找到全局最优解)")

            # ================ 11. 调试输出 ================
            print(
                f"第 {self.gen} 代 | 函数评估次数: {self.FEs} | λ={lambda_val:.2f} "  # 修改：添加代数信息
                f"sPart[CR:{self.mu_CR:.2f},F:{self.mu_F:.2f}] ")
            print("-" * 60)  # 加个分隔符

            # 在循环末尾检查 FEs 是否超限
            if self.FEs >= FEsMaxs:
                print(f"\n达到最大函数评估次数 {FEsMaxs}，在第 {self.gen} 代结束运行。")
                break

        "================while结束===================="
        # ================ 主循环结束 ================
        print("\n" + "=" * 30 + " 算法主循环结束 " + "=" * 30)

        # 6. 返回最佳的个体和它的适应度
        # 输出最佳个体和适应度值
        ic_value = mc_influence(self.G, Gbest, self.p)  # 计算最佳个体的IC值
        print(f"\n最佳个体: {Gbest}, 最佳适应度: {Gbest_fitness:.4f}")  # 打印最佳个体和适应度
        print(f"最佳个体IC值：{ic_value}")
        end_time = time.time()  # 记录函数结束时间
        running_time = end_time - start_time  # 计算运行时间
        print(f"\n总运行时间: {end_time - start_time:.2f}秒")
        
        # 打印局部搜索统计信息
        ls_stats = self.get_local_search_stats()
        print("\n=== 局部搜索成功率统计 ===")
        print(f"整体局部搜索: {ls_stats['overall']['successes']}/{ls_stats['overall']['attempts']} = {ls_stats['overall']['rate']:.2f}%")
        print(f"种群改进搜索: {ls_stats['spart']['successes']}/{ls_stats['spart']['attempts']} = {ls_stats['spart']['rate']:.2f}%")
        print(f"全局最优搜索: {ls_stats['gbest']['successes']}/{ls_stats['gbest']['attempts']} = {ls_stats['gbest']['rate']:.2f}%")


        """一.第一个图"""
        # 绘制图表并保存
        result_filename = f"image/nodes_{self.G.number_of_nodes()}_edges_{self.G.number_of_edges()}_k{k}_FEs{self.FEs}_p{self.p}.png"
        self.plot_results(result_filename)

        """==================二.第2个图==================="""
        now = datetime.datetime.now()
        timestamp = now.strftime("%Y%m%d_%H%M%S")

        output_filename = f"output/nodes_{self.G.number_of_nodes()}_edges_{self.G.number_of_edges()}_k{k}_p{self.p}_{timestamp}.txt"

        # 确保output文件夹存在
        output_dir = "output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        with open(output_filename, "w") as f:
            f.write(f"k: {k}\n")
            f.write(f"种子集: {sorted(Gbest)}\n")
            f.write(f"IC模拟10000次值: {ic_value:.4f}\n")
            f.write(f"运行时间: {running_time:.2f} 秒\n")  # 将运行时间写入文件

            f.write(f"迭代次数: {g}\n")
            f.write(f"激活概率: {self.p}\n")
            f.write("\n适应度历史:\n")
            for i, fitness in enumerate(self.fitness_history):
                f.write(f"代数 {i + 1}: {fitness:.4f}\n")  # 将每代的适应度值写入文件
            f.write("\nLambda 历史:\n")  # 添加 Lambda 历史记录
            for i, lambda_val in enumerate(self.lambda_history):
                f.write(f"代数 {i + 1}: {lambda_val:.4f}\n")
                
            # 添加局部搜索统计信息到输出文件
            ls_stats = self.get_local_search_stats()
            f.write("\n局部搜索成功率统计:\n")
            f.write(f"整体局部搜索: {ls_stats['overall']['successes']}/{ls_stats['overall']['attempts']} = {ls_stats['overall']['rate']:.2f}%\n")
            f.write(f"种群改进搜索: {ls_stats['spart']['successes']}/{ls_stats['spart']['attempts']} = {ls_stats['spart']['rate']:.2f}%\n")
            f.write(f"全局最优搜索: {ls_stats['gbest']['successes']}/{ls_stats['gbest']['attempts']} = {ls_stats['gbest']['rate']:.2f}%\n")

        print(f"\n结果已保存到文件: {output_filename}")

        return self.population, self.fitness_history, self.lambda_history, self.mutation_history,  Gbest, Gbest_fitness, running_time