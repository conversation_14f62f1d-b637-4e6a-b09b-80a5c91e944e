<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .node-text { font-family: Arial, sans-serif; font-size: 11px; text-anchor: middle; }
      .small-text { font-family: Arial, sans-serif; font-size: 9px; text-anchor: middle; }
      .preprocess { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
      .sampling { fill: #e8f5e8; stroke: #388e3c; stroke-width: 2; }
      .quality { fill: #fff3e0; stroke: #f57c00; stroke-width: 3; }
      .diversity { fill: #fce4ec; stroke: #c2185b; stroke-width: 3; }
      .decision { fill: #ffebee; stroke: #d32f2f; stroke-width: 2; }
      .final { fill: #f1f8e9; stroke: #689f38; stroke-width: 4; }
      .group-bg { fill: #f5f5f5; stroke: #999; stroke-width: 1; stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" class="title">ANFDE-IM混合初始化策略详细流程图</text>
  
  <!-- 预处理阶段背景 -->
  <rect x="20" y="60" width="1360" height="120" rx="10" class="group-bg"/>
  <text x="700" y="80" class="subtitle">预处理阶段</text>
  
  <!-- 网络输入 -->
  <rect x="50" y="100" width="120" height="60" rx="10" class="preprocess"/>
  <text x="110" y="120" class="node-text">🌐 网络输入</text>
  <text x="110" y="135" class="small-text">G(V,E)</text>
  <text x="110" y="150" class="small-text">图结构数据</text>
  
  <!-- 桥节点检测 -->
  <rect x="220" y="100" width="140" height="60" rx="10" class="preprocess"/>
  <text x="290" y="115" class="node-text">🔗 桥节点检测</text>
  <text x="290" y="130" class="small-text">介数中心性</text>
  <text x="290" y="145" class="small-text">📊 Top 10%</text>
  
  <!-- 综合中心性 -->
  <rect x="410" y="100" width="140" height="60" rx="10" class="preprocess"/>
  <text x="480" y="115" class="node-text">⚖️ 综合中心性</text>
  <text x="480" y="130" class="small-text">0.6×介数+0.4×度</text>
  <text x="480" y="145" class="small-text">📈 MinMax归一化</text>
  
  <!-- 区域划分 -->
  <rect x="600" y="100" width="140" height="60" rx="10" class="preprocess"/>
  <text x="670" y="115" class="node-text">🗺️ 区域划分</text>
  <text x="670" y="130" class="small-text">基于直径路径</text>
  <text x="670" y="145" class="small-text">📍 距离分层</text>
  
  <!-- 采样生成阶段背景 -->
  <rect x="20" y="200" width="1360" height="120" rx="10" class="group-bg"/>
  <text x="700" y="220" class="subtitle">采样生成阶段</text>
  
  <!-- LHS采样 -->
  <rect x="200" y="240" width="180" height="60" rx="10" class="sampling"/>
  <text x="290" y="255" class="node-text">📐 LHS采样</text>
  <text x="290" y="270" class="small-text">拉丁超立方</text>
  <text x="290" y="285" class="small-text">🎯 SN/2个解</text>
  
  <!-- 启发式采样 -->
  <rect x="450" y="240" width="180" height="60" rx="10" class="sampling"/>
  <text x="540" y="255" class="node-text">🧠 启发式采样</text>
  <text x="540" y="270" class="small-text">度中心性排序</text>
  <text x="540" y="285" class="small-text">⭐ SN/2个解</text>
  
  <!-- 候选解池阶段背景 -->
  <rect x="20" y="340" width="1360" height="120" rx="10" class="group-bg"/>
  <text x="700" y="360" class="subtitle">候选解池</text>
  
  <!-- LHS解集 -->
  <rect x="150" y="380" width="200" height="60" rx="10" class="diversity"/>
  <text x="250" y="395" class="node-text">💎 LHS解集</text>
  <text x="250" y="410" class="small-text">多样性导向</text>
  <text x="250" y="425" class="small-text">🔄 空间均匀分布</text>
  
  <!-- 启发式解集 -->
  <rect x="450" y="380" width="200" height="60" rx="10" class="quality"/>
  <text x="550" y="395" class="node-text">⚡ 启发式解集</text>
  <text x="550" y="410" class="small-text">质量导向</text>
  <text x="550" y="425" class="small-text">🎯 高中心性节点</text>
  
  <!-- 第一阶段：质量筛选背景 -->
  <rect x="20" y="480" width="1360" height="160" rx="10" class="group-bg"/>
  <text x="700" y="500" class="subtitle">第一阶段：质量筛选</text>
  
  <!-- 并行适应度计算 -->
  <rect x="100" y="520" width="180" height="60" rx="10" class="quality"/>
  <text x="190" y="535" class="node-text">⚡ 并行适应度计算</text>
  <text x="190" y="550" class="small-text">多线程LIE评估</text>
  <text x="190" y="565" class="small-text">📈 缓存优化</text>
  
  <!-- 质量排序 -->
  <ellipse cx="400" cy="550" rx="80" ry="30" class="decision"/>
  <text x="400" y="545" class="node-text">🏆 质量排序</text>
  <text x="400" y="560" class="small-text">基于LIE值</text>
  
  <!-- 高质量解集 -->
  <rect x="550" y="520" width="160" height="60" rx="10" class="quality"/>
  <text x="630" y="535" class="node-text">🥇 高质量解集</text>
  <text x="630" y="550" class="small-text">前70%解</text>
  <text x="630" y="565" class="small-text">✨ quality_ratio=0.7</text>
  
  <!-- 低质量解集 -->
  <rect x="750" y="520" width="160" height="60" rx="10" class="diversity"/>
  <text x="830" y="535" class="node-text">🥈 低质量解集</text>
  <text x="830" y="550" class="small-text">后30%解</text>
  <text x="830" y="565" class="small-text">💤 备选池</text>
  
  <!-- 第二阶段：多样性筛选背景 -->
  <rect x="20" y="660" width="1360" height="160" rx="10" class="group-bg"/>
  <text x="700" y="680" class="subtitle">第二阶段：多样性筛选</text>
  
  <!-- Jaccard相似度计算 -->
  <rect x="100" y="700" width="180" height="60" rx="10" class="diversity"/>
  <text x="190" y="715" class="node-text">🎨 Jaccard相似度计算</text>
  <text x="190" y="730" class="small-text">成对比较</text>
  <text x="190" y="745" class="small-text">📏 sim_threshold=0.8</text>
  
  <!-- 多样性过滤 -->
  <ellipse cx="400" cy="730" rx="80" ry="30" class="decision"/>
  <text x="400" y="725" class="node-text">🌈 多样性过滤</text>
  <text x="400" y="740" class="small-text">相似度检查</text>
  
  <!-- 多样性解集 -->
  <rect x="550" y="700" width="160" height="60" rx="10" class="diversity"/>
  <text x="630" y="715" class="node-text">🎯 多样性解集</text>
  <text x="630" y="730" class="small-text">相似度≤0.8</text>
  <text x="630" y="745" class="small-text">🌟 高质量+高多样性</text>
  
  <!-- 相似解集 -->
  <rect x="750" y="700" width="160" height="60" rx="10" class="quality"/>
  <text x="830" y="715" class="node-text">❌ 相似解集</text>
  <text x="830" y="730" class="small-text">相似度>0.8</text>
  <text x="830" y="745" class="small-text">🔄 被过滤解</text>
  
  <!-- 解补充阶段背景 -->
  <rect x="20" y="840" width="1360" height="120" rx="10" class="group-bg"/>
  <text x="700" y="860" class="subtitle">解补充阶段</text>
  
  <!-- 种群大小检查 -->
  <ellipse cx="300" cy="900" rx="80" ry="30" class="decision"/>
  <text x="300" y="895" class="node-text">📏 种群大小检查</text>
  <text x="300" y="910" class="small-text">|P| < N ?</text>
  
  <!-- 随机补充 -->
  <rect x="450" y="870" width="160" height="60" rx="10" class="sampling"/>
  <text x="530" y="885" class="node-text">🎲 随机补充</text>
  <text x="530" y="900" class="small-text">桥节点优先</text>
  <text x="530" y="915" class="small-text">🔧 确保种群完整</text>
  
  <!-- 截取前N个 -->
  <rect x="650" y="870" width="160" height="60" rx="10" class="sampling"/>
  <text x="730" y="885" class="node-text">✂️ 截取前N个</text>
  <text x="730" y="900" class="small-text">保持种群大小</text>
  <text x="730" y="915" class="small-text">📊 精确N个解</text>
  
  <!-- 最终初始种群 -->
  <rect x="850" y="870" width="200" height="60" rx="10" class="final"/>
  <text x="950" y="885" class="node-text">✨ 最终初始种群</text>
  <text x="950" y="900" class="small-text">N个个体</text>
  <text x="950" y="915" class="small-text">🎯 质量+多样性平衡</text>
  
  <!-- 连接线 -->
  <!-- 预处理阶段连接 -->
  <line x1="170" y1="130" x2="220" y2="130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="360" y1="130" x2="410" y2="130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="550" y1="130" x2="600" y2="130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 到采样阶段 -->
  <line x1="290" y1="160" x2="290" y2="240" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="480" y1="160" x2="290" y2="240" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="670" y1="160" x2="290" y2="240" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="480" y1="160" x2="540" y2="240" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="290" y1="160" x2="540" y2="240" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 到候选解池 -->
  <line x1="290" y1="300" x2="250" y2="380" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="540" y1="300" x2="550" y2="380" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 到质量筛选 -->
  <line x1="250" y1="440" x2="190" y2="520" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="550" y1="440" x2="190" y2="520" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="280" y1="550" x2="320" y2="550" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="480" y1="550" x2="550" y2="550" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="480" y1="550" x2="750" y2="550" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 到多样性筛选 -->
  <line x1="630" y1="580" x2="190" y2="700" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="280" y1="730" x2="320" y2="730" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="480" y1="730" x2="550" y2="730" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="480" y1="730" x2="750" y2="730" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 到解补充 -->
  <line x1="630" y1="760" x2="300" y2="870" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="830" y1="580" x2="300" y2="870" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="380" y1="900" x2="450" y2="900" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="220" y1="900" x2="650" y2="900" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="610" y1="900" x2="850" y2="900" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="810" y1="900" x2="850" y2="900" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 标注 -->
  <text x="390" y="885" class="small-text">是</text>
  <text x="240" y="885" class="small-text">否</text>
</svg>
