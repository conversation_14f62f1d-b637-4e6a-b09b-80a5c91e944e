<?xml version="1.0" encoding="UTF-8"?>
<svg width="150" height="35" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .output-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .seed { fill: #66bb6a; stroke: #2e7d32; stroke-width: 1; }
      .best { fill: #4caf50; stroke: #1b5e20; stroke-width: 1.5; }
      .link { stroke: #81c784; stroke-width: 0.8; opacity: 0.8; }
      .end-icon { fill: #f44336; }
      .success-icon { fill: #4caf50; }
      .text { font-family: Arial, sans-serif; font-size: 9px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
    </style>
  </defs>
  
  <!-- 输出容器 -->
  <rect x="5" y="8" width="140" height="20" rx="10" class="output-bg"/>
  
  <!-- END标识 -->
  <g transform="translate(20,18)">
    <circle cx="0" cy="0" r="4" class="end-icon"/>
    <text x="0" y="1" style="font-size:3px; text-anchor:middle; fill:#fff; font-weight:bold;">END</text>
  </g>
  
  <!-- 种子网络 -->
  <g transform="translate(75,18)">
    <circle cx="-12" cy="-2" r="2" class="seed"/>
    <circle cx="-4" cy="0" r="2.5" class="best"/>
    <circle cx="4" cy="-2" r="2" class="seed"/>
    <circle cx="12" cy="2" r="1.5" class="seed"/>
    
    <line x1="-12" y1="-2" x2="-4" y2="0" class="link"/>
    <line x1="-4" y1="0" x2="4" y2="-2" class="link"/>
    <line x1="-4" y1="0" x2="12" y2="2" class="link"/>
    
    <text x="-4" y="-5" style="font-size:3px; text-anchor:middle; fill:#1b5e20;">★</text>
  </g>
  
  <!-- 成功标识 -->
  <g transform="translate(130,18)">
    <circle cx="0" cy="0" r="4" class="success-icon"/>
    <path d="M -2,-0.5 L -0.5,1.5 L 2.5,-1.5" fill="none" stroke="#fff" stroke-width="1" stroke-linecap="round"/>
  </g>
  
  <!-- 标题 -->
  <text x="75" y="6" class="text">🏆 最优种子集输出</text>
  <text x="75" y="33" style="font-family:Arial; font-size:6px; text-anchor:middle; fill:#666;">循环结束 → 种子输出 → 完成</text>
</svg>
