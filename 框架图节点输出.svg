<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="50" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .node-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .seed-dot { fill: #66bb6a; stroke: #2e7d32; stroke-width: 1; }
      .best-dot { fill: #4caf50; stroke: #1b5e20; stroke-width: 1.5; }
      .connection { stroke: #81c784; stroke-width: 1; opacity: 0.7; }
      .success { fill: #4caf50; }
      .trophy { fill: #ffd700; stroke: #ff8f00; stroke-width: 1; }
      .text { font-family: Arial, sans-serif; font-size: 10px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
      .small-text { font-family: Arial, sans-serif; font-size: 7px; text-anchor: middle; fill: #4caf50; }
    </style>
  </defs>
  
  <!-- 主容器 -->
  <rect x="10" y="10" width="180" height="30" rx="15" class="node-bg"/>
  
  <!-- 左侧：循环结束标识 -->
  <g transform="translate(30,25)">
    <circle cx="0" cy="0" r="6" fill="#f44336"/>
    <rect x="-3" y="-2" width="6" height="4" rx="1" fill="#fff"/>
    <text x="0" y="1" style="font-size:3px; text-anchor:middle; fill:#f44336; font-weight:bold;">END</text>
  </g>
  
  <!-- 中间：种子集合 -->
  <g transform="translate(100,25)">
    <!-- 种子节点 -->
    <circle cx="-15" cy="-3" r="2.5" class="seed-dot"/>
    <circle cx="-5" cy="0" r="3" class="best-dot"/>
    <circle cx="5" cy="-3" r="2.5" class="seed-dot"/>
    <circle cx="15" cy="3" r="2" class="seed-dot"/>
    
    <!-- 连接线 -->
    <line x1="-15" y1="-3" x2="-5" y2="0" class="connection"/>
    <line x1="-5" y1="0" x2="5" y2="-3" class="connection"/>
    <line x1="-5" y1="0" x2="15" y2="3" class="connection"/>
    
    <!-- 最优标识 -->
    <text x="-5" y="-7" style="font-size:4px; text-anchor:middle; fill:#1b5e20;">★</text>
  </g>
  
  <!-- 右侧：成功+奖杯 -->
  <g transform="translate(170,25)">
    <circle cx="-8" cy="0" r="5" class="success"/>
    <path d="M -10,-1 L -8,2 L -6,-2" fill="none" stroke="#fff" stroke-width="1" stroke-linecap="round"/>
    
    <ellipse cx="3" cy="-1" rx="4" ry="2.5" class="trophy"/>
    <rect x="1" y="1.5" width="4" height="2" rx="0.5" fill="#8d6e63"/>
  </g>
  
  <!-- 文字说明 -->
  <text x="100" y="8" class="text">🏁 输出最优种子集</text>
  <text x="100" y="47" class="small-text">算法收敛完成，输出影响力最大化种子节点</text>
</svg>
