\documentclass[12pt]{article}
\usepackage[utf8]{inputenc}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{xeCJK}
\usepackage{geometry}
\geometry{a4paper,margin=2.5cm}

\title{ANFDE-IM算法设计与实现}
\author{研究团队}
\date{\today}

\begin{document}

\maketitle

\section{提出的算法}

本节详细介绍基于地形状态感知的自适应差分进化算法（Landscape-Aware Adaptive Differential Evolution, LADE）。LADE引入地形状态建模与自适应算子调度机制，使搜索行为能够实时感知解空间的结构特征，并能根据搜索过程中种群分布的变化，灵活调整全局探索与局部开发策略。其核心框架如图1所示。首先，混合初始化模块结合拉丁超立方采样与基于度中心性的启发式采样，有效生成兼具高质量和多样性的初始种群，为后续搜索奠定坚实基础。随后，地形状态感知模块利用传播差异指数（SDI）动态计算地形状态值λ，实现对搜索空间结构特征的实时感知。基于λ值，状态驱动的算子调度模块将搜索过程划分为四种不同状态，并自适应选择差分进化算子，从而在全局探索与局部开发之间实现灵活切换。参数自适应模块则依据搜索反馈动态调整交叉概率（CR）与缩放因子（F）。在此基础上，局部搜索模块针对当前优质解进行邻域精细搜索，持续优化解的质量。下文将对LADE关键模块的设计与实现细节进行详细阐述。

\subsection{采样方法}

为生成高质量且多样化的初始种群，LADE采用混合初始化策略，结合拉丁超立方采样（Latin Hypercube Sampling, LHS）和基于度中心性的启发式采样。

\subsubsection{拉丁超立方采样（LHS）}

拉丁超立方采样是一种分层采样技术，能够在多维空间中生成均匀分布的样本点。针对影响力最大化问题，我们将网络空间视为$d$维采样空间，其中每一维对应一个网络区域。

\textbf{网络区域划分：}首先将网络$G=(V,E)$划分为$d$个区域$R=\{R_0,R_1,...,R_{d-1}\}$。区域划分采用基于网络直径路径的策略，即
\begin{equation}
R_i=\left\{v\in V:\min_{u\in P}d\left(v,u\right)=i\right\}
\end{equation}

其中$P$为网络直径路径，$d(v,u)$为节点$v$到$u$的最短路径长度。最终，网络被划分为$d$个区域，每个区域$R_j$作为LHS采样的一个维度。

\textbf{LHS采样过程：}设采样数量为$N$，每组采样$X^{(i)}=[x_1^{(i)},x_2^{(i)},\ldots,x_d^{(i)}]$，其中$x_j^{(i)}$为第$i$组采样在第$j$维（区域$R_j$）的分布点。具体操作如下：

\begin{enumerate}
\item 对每一维$j=1,\ldots,d$，生成$[1,N]$的随机排列$\pi_j$。
\item 对每组采样$i=1,\ldots,N$，生成均匀分布随机变量$U_j^{(i)}\sim Uniform(0,1)$
\item 计算归一化采样位置：
\begin{equation}
z_j^{(i)}=\frac{\pi_j(i)-U_j^{(i)}}{N}
\end{equation}
\item 节点索引映射：
\begin{equation}
\mathrm{idx}_j^{(i)}=\left\lfloor z_j^{(i)}\cdot|R_j|\right\rfloor
\end{equation}
其中$|R_j|$为第$j$区域节点数，$\lfloor\cdot\rfloor$表示向下取整运算。
\item 选取节点：
\begin{equation}
v_j^{(i)}=\mathrm{list}(R_j)[\mathrm{idx}_j^{(i)}]
\end{equation}
其中$\mathrm{list}(R_j)$为$R_j$中节点的有序列表。
\item 得到第$i$组采样解
\begin{equation}
S^{(i)} = \{ v_1^{(i)}, v_2^{(i)}, \ldots, v_d^{(i)} \}
\end{equation}
\end{enumerate}

\textbf{三阶段补全机制：}若采样节点不足$k$，采用三阶段补全策略。首先，遍历所有区域，从每个当前未被采样节点覆盖的区域各补充一个综合中心性最高的节点（每区域仅补充一次），直到达到$k$或区域遍历完成。若仍不足，则优先从桥节点中选择综合中心性高的节点补齐；若仍有缺口，则从所有同时不属于任何区域且未被选中的节点（即外围节点）中，按综合中心性降序递补至$k$。其中，综合中心性指节点的度中心性与介数中心性归一化加权之和。

\subsubsection{度中心性采样}

对于给定的种子集规模$k$和采样数量$SN$，度中心性采样首先选择图中度数最高的$k$个节点，作为基础解。随后，对该解中的每个节点以0.5的概率进行扰动，即用图中未被当前解包含的随机节点进行替换。

算法随后在基于度中心性与拉丁超立方采样生成的采样解中，筛选代表性与多样性兼具的解用于初始化种群。

\subsection{初始化策略}

初始化阶段采用混合采样策略，首先分别通过拉丁超立方采样（LHS）和基于度中心性的启发式采样生成两组候选解，前者保障了解空间的均匀覆盖，后者突出局部高质量节点组合。随后，将两组候选解合并，并按照适应度值分别筛选出每类采样中表现最优的个体，再将高质量解集合并，基于Jaccard相似度进行多样性过滤，去除结构高度相似的冗余解。若筛选后种群规模不足，则优先补充剩余适应度较高的解，最终得到兼具适应度与结构多样性的高质量初始种群。

\begin{algorithm}[H]
\caption{混合初始化策略}
\label{alg:hybrid_initialization}
\begin{algorithmic}[1]
\REQUIRE 网络$G(V,E)$，种子集大小$k$，种群大小$N$，传播概率$p$，采样数量$SN$，质量比例$q=0.5$，相似度阈值$\theta=0.8$
\ENSURE 初始种群$P$
\STATE // 第一阶段：网络预处理
\STATE $bridge\_nodes \leftarrow \text{DetectBridgeNodes}(G)$
\STATE $combined\_scores \leftarrow \text{CalculateCombinedCentrality}(G)$
\STATE $regions \leftarrow \text{PartitionByDiameter}(G)$
\STATE 
\STATE // 第二阶段：混合采样
\STATE $lhs\_solutions \leftarrow \text{SampleLHS}(G, k, SN/2, regions, bridge\_nodes, combined\_scores)$
\STATE $score\_solutions \leftarrow \text{SampleDegree}(G, k, SN/2)$
\STATE 
\STATE // 第三阶段：适应度计算
\STATE $all\_solutions \leftarrow lhs\_solutions \cup score\_solutions$
\FOR{$S \in all\_solutions$}
    \STATE $fitness[S] \leftarrow \text{EDV}(S)$
\ENDFOR
\STATE 
\STATE // 第四阶段：质量筛选
\STATE $n\_quality \leftarrow \lfloor N \times q \rfloor$
\STATE $lhs\_quality \leftarrow \text{SelectTopK}(lhs\_solutions, fitness, n\_quality)$
\STATE $score\_quality \leftarrow \text{SelectTopK}(score\_solutions, fitness, n\_quality)$
\STATE $combined\_solutions \leftarrow lhs\_quality \cup score\_quality$
\STATE 
\STATE // 第五阶段：多样性筛选
\STATE $P \leftarrow []$
\STATE $excluded \leftarrow \emptyset$
\FOR{$i = 0$ to $|combined\_solutions| - 1$}
    \IF{$i \notin excluded$}
        \STATE $P.\text{append}(combined\_solutions[i])$
        \STATE $current\_set \leftarrow \text{Set}(combined\_solutions[i])$
        \FOR{$j = i+1$ to $|combined\_solutions| - 1$}
            \IF{$j \notin excluded$}
                \STATE $other\_set \leftarrow \text{Set}(combined\_solutions[j])$
                \STATE $similarity \leftarrow \frac{|current\_set \cap other\_set|}{|current\_set \cup other\_set|}$
                \IF{$similarity > \theta$}
                    \STATE $excluded.\text{add}(j)$
                \ENDIF
            \ENDIF
        \ENDFOR
    \ENDIF
\ENDFOR
\STATE 
\STATE // 第六阶段：种群补充
\STATE $remaining\_solutions \leftarrow combined\_solutions \setminus P$
\WHILE{$|P| < N$ AND $remaining\_solutions \neq \emptyset$}
    \STATE $best\_solution \leftarrow \arg\max_{S \in remaining\_solutions} fitness[S]$
    \STATE $P.\text{append}(best\_solution)$
    \STATE $remaining\_solutions.\text{remove}(best\_solution)$
\ENDWHILE
\STATE 
\WHILE{$|P| < N$}
    \STATE $bridge\_sample \leftarrow \text{RandomSample}(bridge\_nodes, \min(2, |bridge\_nodes|))$
    \STATE $non\_bridge\_nodes \leftarrow V \setminus bridge\_nodes$
    \STATE $non\_bridge\_sample \leftarrow \text{RandomSample}(non\_bridge\_nodes, k - |bridge\_sample|)$
    \STATE $new\_solution \leftarrow bridge\_sample \cup non\_bridge\_sample$
    \STATE $P.\text{append}(new\_solution)$
\ENDWHILE
\STATE 
\STATE // 第七阶段：去重处理
\STATE $unique\_solutions \leftarrow \text{RemoveDuplicates}(P)$
\RETURN $unique\_solutions[:N]$
\end{algorithmic}
\end{algorithm}

\begin{algorithm}[H]
\caption{拉丁超立方采样}
\label{alg:lhs_sampling}
\begin{algorithmic}[1]
\REQUIRE 网络$G(V,E)$，种子集大小$k$，采样数量$N$，区域划分$regions$，桥节点$bridge\_nodes$，综合评分$combined\_scores$
\ENSURE LHS采样解集$Solutions$
\STATE $Solutions \leftarrow []$
\FOR{$i = 1$ to $N$}
    \STATE $effective\_dims \leftarrow \min(k, |regions|)$
    \STATE $lhs\_sample \leftarrow \text{GenerateLHS}(effective\_dims, 1)$
    \STATE $solution \leftarrow \emptyset$
    \FOR{$j = 1$ to $effective\_dims$}
        \STATE $region \leftarrow regions[j]$
        \IF{$region \neq \emptyset$}
            \STATE $idx \leftarrow \lfloor lhs\_sample[j] \times |region| \rfloor$
            \STATE $solution \leftarrow solution \cup \{region[idx]\}$
        \ENDIF
    \ENDFOR
    \STATE 
    \STATE // 三阶段补充机制
    \IF{$|solution| < k$}
        \STATE $remaining \leftarrow k - |solution|$
        \STATE // 阶段1：区域补充
        \FOR{$region \in regions$}
            \IF{$remaining > 0$ AND $region \cap solution = \emptyset$}
                \STATE $best\_node \leftarrow \arg\max_{v \in region} combined\_scores[v]$
                \STATE $solution \leftarrow solution \cup \{best\_node\}$
                \STATE $remaining \leftarrow remaining - 1$
            \ENDIF
        \ENDFOR
        \STATE // 阶段2：桥节点补充
        \STATE $bridge\_candidates \leftarrow bridge\_nodes \setminus solution$
        \WHILE{$remaining > 0$ AND $bridge\_candidates \neq \emptyset$}
            \STATE $selected\_bridge \leftarrow \text{RandomChoice}(bridge\_candidates)$
            \STATE $solution \leftarrow solution \cup \{selected\_bridge\}$
            \STATE $bridge\_candidates \leftarrow bridge\_candidates \setminus \{selected\_bridge\}$
            \STATE $remaining \leftarrow remaining - 1$
        \ENDWHILE
        \STATE // 阶段3：外围节点补充
        \STATE $peripheral\_nodes \leftarrow V \setminus \bigcup regions$
        \STATE $peripheral\_candidates \leftarrow peripheral\_nodes \setminus solution$
        \WHILE{$remaining > 0$ AND $peripheral\_candidates \neq \emptyset$}
            \STATE $sorted\_peripheral \leftarrow \text{SortByDescending}(peripheral\_candidates, combined\_scores)$
            \STATE $selected\_peripheral \leftarrow sorted\_peripheral[0]$
            \STATE $solution \leftarrow solution \cup \{selected\_peripheral\}$
            \STATE $peripheral\_candidates \leftarrow peripheral\_candidates \setminus \{selected\_peripheral\}$
            \STATE $remaining \leftarrow remaining - 1$
        \ENDWHILE
        \STATE // 最终随机补充
        \WHILE{$remaining > 0$}
            \STATE $all\_candidates \leftarrow V \setminus solution$
            \STATE $selected \leftarrow \text{RandomChoice}(all\_candidates)$
            \STATE $solution \leftarrow solution \cup \{selected\}$
            \STATE $remaining \leftarrow remaining - 1$
        \ENDWHILE
    \ENDIF
    \STATE $Solutions.\text{append}(solution)$
\ENDFOR
\RETURN $Solutions$
\end{algorithmic}
\end{algorithm}

\begin{algorithm}[H]
\caption{度中心性采样}
\label{alg:degree_sampling}
\begin{algorithmic}[1]
\REQUIRE 网络$G(V,E)$，种子集大小$k$，采样数量$N$
\ENSURE 度中心性采样解集$Solutions$
\STATE $Solutions \leftarrow []$
\STATE $top\_k\_nodes \leftarrow \text{GetTopKDegreeNodes}(G, k)$
\FOR{$i = 1$ to $N$}
    \STATE $solution \leftarrow top\_k\_nodes.\text{copy}()$
    \FOR{$j = 0$ to $k-1$}
        \IF{$\text{Random}() > 0.5$}
            \STATE $available\_nodes \leftarrow V \setminus solution$
            \IF{$available\_nodes \neq \emptyset$}
                \STATE $new\_node \leftarrow \text{RandomChoice}(available\_nodes)$
                \STATE $solution[j] \leftarrow new\_node$
            \ENDIF
        \ENDIF
    \ENDFOR
    \STATE $Solutions.\text{append}(solution)$
\ENDFOR
\RETURN $Solutions$
\end{algorithmic}
\end{algorithm}

\end{document}
