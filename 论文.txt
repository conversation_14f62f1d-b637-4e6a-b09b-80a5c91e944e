\section{Introduction}
\label{sec1}
%% Labels are used to cross-reference an item using \ref command.
Social networks are interconnected systems formed through various social relationships among individuals, playing an increasingly prominent role in domains such as information dissemination, public opinion evolution, and precision marketing.
The rapid proliferation of platforms such as Weibo, TikTok, and Twitter has led to the emergence of highly intricate information flow systems shaped by user relationships and behavioral interactions.
Against this backdrop, the Influence Maximization (IM) problem~\cite{RN1} has become one of the core challenges in social network analysis, with the objective of identifying a minimal set of seed nodes that maximize the expected spread under a specified diffusion model.
The problem is characterized by both fundamental theoretical challenges and practical significance, and has attracted sustained attention across multiple disciplines~\cite{guney2021large,wang2022maximizing,rui2023scalable}.

Since <PERSON><PERSON> et al.~\cite{RN2} first formalized the influence maximization problem as an algorithmic task.
<PERSON><PERSON> et al.~\cite{RN3} modeled the problem based on information diffusion models, formally defined it as a combinatorial optimization problem under the Independent Cascade (IC) and Linear Threshold (LT) models, and rigorously proved it to be NP-hard.
This work laid the theoretical foundation for the IM problem and, building upon this foundation, proposed a greedy algorithm framework grounded in the principle of submodularity. The algorithm was proven to achieve an optimal approximation ratio of $1-\frac{1}{e}$ for monotone submodular functions.
Despite strong theoretical guarantees, the greedy algorithm exhibits high computational overhead due to its reliance on large-scale Monte Carlo simulations, and its scalability to large networks is therefore significantly constrained.
Subsequent improvements, such as Cost-Efficient Lazy Forward (CELF)~\cite{RN4} and CELF++~\cite{RN5}, partially alleviated the computational burden but did not fundamentally eliminate the dependency on costly simulations. 
To overcome these limitations, a series of structural heuristic methods, including DC+~\cite{RN6}, and the fusion gravity model ~\cite{RN7}, were introduced. 
These methods rapidly evaluate node influence by integrating local and semi-global features such as node degree, k-shell, and gravity models. 
Although such methods demonstrate favorable computational efficiency, their reliance on static topological features and lack of diffusion modeling capability lead to solution quality that varies significantly with network structure, without guaranteeing optimality.
To balance search efficiency and solution quality, metaheuristic algorithms have been progressively introduced into the solution process of the IM problem. These algorithms possess global search capabilities and do not rely on gradient information, offering inherent advantages in addressing such complex optimization tasks (e.g., Particle Swarm Optimization \cite{RN9}, Differential Evolution~\cite{RN10}).
However, the solution space of the IM problem exhibits strong non-convexity, multimodality, and high nonlinearity in propagation responses~\cite{RN11}, which tends to trap the population in local structural basins and hinders effective transitions.
The blind search behavior and the lack of state awareness in metaheuristic algorithms give rise to inherent challenges in solving the IM problem. 
First, the lack of dynamic modeling between search behavior and landscape structure impedes strategy adaptation based on population evolution.
Second, the absence of a feedback mechanism hinders both the detection and escape from local optima, which in turn reduces global search capacity and convergence stability.
At its core, the lack of structural perception constitutes a fundamental bottleneck that restricts the adaptability of metaheuristic algorithms in complex search spaces.

Fitness landscape theory provides an effective tool for analyzing the structural properties of the solution space and the dynamic characteristics of search behavior in optimization problems, which facilitates a deeper understanding of population evolution mechanisms~\cite{RN12}.
Previous studies have shown that incorporating fitness landscape analysis into metaheuristic algorithms can effectively mitigate the blindness of search behaviors and improve algorithmic performance~\cite{RN13}.
As a result, the integration of fitness landscape theory into metaheuristic algorithm design has gradually evolved into an effective methodological paradigm for addressing complex optimization problems.

This study introduces fitness landscape analysis to construct a structure–behavior mapping mechanism that enhances the algorithm’s ability to recognize search states and adjust strategies adaptively.
Specifically, a landscape state-aware differential evolution algorithm (LADE) is proposed.
A multi-dimensional set of landscape state indicators is designed for the IM problem, with its core represented by a landscape state value that dynamically characterizes the coupling between search behavior and structural properties of the solution space.
Building upon this, a state-driven operator scheduling mechanism is developed to enable adaptive switching and transition of search strategies across different evolutionary stages, thereby improving the global search capability and convergence stability of the algorithm.
The main contributions of this paper are summarized as follows.
\begin{itemize}[noitemsep]
\item Construct a landscape state modeling mechanism: define a landscape state value $\lambda$ by integrating multidimensional indicators of individual distribution and propagation behavior characteristics to enable dynamic coupling modeling between search behavior and the structure of the solution space.
\item Propose a state-adaptive operator scheduling mechanism: partition the search process into four states—convergence, exploitation, exploration, and escape—driven by the landscape state value $\lambda$, adaptively match mutation operators to these states, and introduce an escape mechanism to actively jump out of local optima basins; this mechanism significantly enhances the global search capability.
\end{itemize}

The remainder of the paper is organized as follows. 
Section~\ref{sec:related} reviews related work on influence maximization and fitness landscape analysis. 
Section~\ref{sec:prelim} introduces the preliminaries of the proposed approach. 
Section~\ref{sec:implementation} details the implementation of LADE. 
Section~\ref{sec:experiment} reports the experimental results and analysis. 
Section~\ref{sec:discussion} discusses the distinctions and limitations of LADE. 
Section~\ref{sec:conclusion} concludes the paper and outlines future research directions.

\section{Related Work}
\label{sec:related}
\subsection{Approximation-Based Methods}
Research on the IM problem originated from the seminal work of Kempe et al.~\cite{RN3}, who proposed a classical greedy algorithm that iteratively selects nodes with the highest marginal gain and achieves the theoretical approximation ratio of $(1 - 1/e)$ for the first time.
However, the algorithm exhibits significant computational bottlenecks: Each evaluation of a node’s marginal gain requires tens of thousands of diffusion simulations, and repeated calculations in the iterative process lead to considerable redundant overhead.
CELF~\cite{RN4} and CELF++~\cite{RN5} introduced lazy update strategies to reduce redundancy, but simulation overhead remains substantial in large-scale networks.
In response to this challenge, a sampling framework based on reverse reachable (RR) sets was proposed~\cite{borgs2014maximizing}.
Representative methods such as IMM~\cite{tang2015influence}, TIM/TIM+~\cite{tang2014influence}, and SSA/D-SSA~\cite{nguyen2016stop} reformulate influence estimation as a set cover problem and thereby achieve notable improvements in computational efficiency.
As network size and accuracy requirements increase, the sampling and storage overhead of RR sets increases significantly and constitutes a new computational bottleneck.

To alleviate this issue, Guo et al.~\cite{guo2020influence} proposed SUBSIM, which enhances efficiency via subset sampling, and HIST, which reduces memory usage through a two-phase design.
Shahrouz et al.~\cite{shahrouz2021gim} introduced parallel methods such as gIM, leveraging GPU acceleration to significantly speed up sampling.
In addition, Rui et al.~\cite{rui2025scalable} proposed a scalable approximation framework for fair influence maximization.
By designing an unbiased estimator, the method effectively controls sampling size and achieves fair influence maximization on large-scale networks.

Overall, approximation methods provide notable advantages in accuracy and theoretical guarantees, but computational and storage costs remain key challenges for large-scale networks.
\subsection{Heuristic Methods}
Compared with the theoretical guarantees provided by approximation-based approaches, heuristic methods have been widely applied to influence maximization tasks in large-scale networks due to their simplicity and computational efficiency.
Shen et al.~\cite{zhong2022identification} introduced the Local Dimension Degree (LDD) method, which utilizes changes in the number of multi-level neighbors to improve the accuracy of key node identification.
Wang et al.~\cite{RN6} proposed a method that integrates centripetal centrality and an exclusion strategy based on multi-structural information, effectively reducing redundancy among seed nodes.
Chen et al.~\cite{RN7}developed a composite index combining node degree and average neighbor degree, along with an extension based on the gravity model, thereby enhancing the distinction of nodes with similar degrees and improving the overall propagation effect.
Guo et al.~\cite{guo2024node} presented the fusion gravity model , which incorporates node degree, K-shell, and eigenvector features to achieve a unified evaluation of both local and global node influence, resulting in greater adaptability across various network types.
Yang et al.~\cite{yang2023new}introduced a recursive clustering method based on community structure and a “peak-slope-valley” topological potential framework, enabling the decentralized placement of seed nodes across multiple communities and significantly increasing both propagation coverage and algorithmic robustness.

Although heuristic methods offer high efficiency and practical value, the complexity of network structures still constrains overall performance and the quality of solutions, highlighting the urgent need for more advanced approaches.
\subsection{Machine Learning-Based Methods}
Recent advances in machine learning, especially in deep learning and graph neural networks (GNNs), have provided novel solutions to the IM problem.
Such models utilize automated feature extraction and hierarchical structure modeling, which enables the integration of complex network properties and significantly improves the representation of node influence.
The ToupleGDD framework by Chen et al.~\cite{chen2023touplegdd} applies deep reinforcement learning and GNNs for IM, enabling effective influence modeling and strong generalization across networks.
The HCCKshell method proposed by Li et al.~\cite{li2024hcckshell} combines pre-trained language models with graph convolutional networks. Through weighted fusion of heterogeneous content and structural features together with entropy-based measures, this approach enhances node representations, diversity, and propagation coverage.
The GCNT model developed by Tang et al.~\cite{tang2024gcnt} integrates GCNs and graph Transformers, and incorporates multi-dimensional centrality and dynamic label mechanisms to strengthen the modeling of network relationships.
The BiGDN framework designed by Zhu et al.~\cite{zhu2025bigdn} centers on GNNs and deep reinforcement learning, and it adopts knowledge distillation and pre-training strategies, which help achieve both inference speed and prediction accuracy on large-scale networks.

Existing methods face limitations due to overlapping propagation paths and insufficient label design, resulting in low discriminability and quality of training labels.
On large-scale networks, frequent node representation and influence computations during the training phase lead to considerable computational complexity and resource consumption. 
\subsection{Metaheuristic-Based Methods}
Metaheuristic algorithms, recognized for their global search capabilities and adaptability, have emerged as effective tools in overcoming the limitations of traditional heuristic approaches for IM.
Gong et al.~\cite{RN9} were the first to propose discrete particle swarm optimization for influence maximization, reformulating the problem as the optimization of a local influence evaluation function  and designing discrete position and velocity update rules to improve diffusion estimation accuracy, though high computational complexity remains a bottleneck.
Subsequently, Tang et al.~\cite{tang2020discrete} enhanced local search and jumping capabilities through a discrete shuffled frog-leaping algorithm.
Khatri et al.~\cite{khatri2023influence} proposed a discrete Harris hawks optimization algorithm, where adaptive initialization and neighborhood detection strengthen the exploration of community structures.
In the evolutionary algorithm domain, Qiu et al.~\cite{RN10} developed a differential evolution algorithm based on a local-influence-descending search strategy, which improves the precision of seed node selection. However, the high complexity of influence estimation affects the algorithm’s efficiency.
To address this, Biswas et al.~\cite{biswas2022two} employed a two-stage VIKOR-assisted differential evolution, utilizing candidate pool compression and multiple mutation operators to enhance search efficiency in large-scale networks, but the quality of the candidate pool remains a challenge.
Wang et al.~\cite{wang2023multi}introduced a multi-operator evolutionary framework to increase diversity and adaptability; however, operator scheduling and parameter tuning are complex.
Chen et al.~\cite{chen2025imune} proposed IMUNE for dynamic network scenarios, specifically targeting spatio-temporal variations in UAV network structures by designing dynamic candidate set update and seed selection mechanisms, thereby improving influence coverage performance in time-varying environments.
Zhu et al.~\cite{zhu2024phee} presented PHEE, which achieves a balance between solution diversity and convergence through stage-wise evaluation and simulated annealing.
Overall, metaheuristic algorithms demonstrate significant advantages in influence maximization by improving global search ability and reducing reliance on high-cost simulations.
\subsection{Fitness Landscape Analysis}
First introduced by Wright\cite{wright1932roles}, fitness landscape theory constitutes a fundamental framework connecting optimization search space structures and algorithm performance, and has driven advances from evolutionary biology to intelligent optimization~\cite{stadler2002fitness}.
Stadler’s triplet modeling provided a foundation for landscape analysis in both continuous and combinatorial optimization~\cite{stadler2002fitness}.
Recent developments have been achieved in theoretical modeling, metric tools, and applications of fitness landscape analysis.

Applications in dynamic optimization and evolutionary processes have focused on landscape variability and robustness.
Landscape rotation strategies, by adjusting objective mappings, improve dynamic adaptability of algorithms~\cite{alza2022analysing}.
In evolutionary biology, hybridization and novel mutations reduce ruggedness and enhance accessibility of landscapes~\cite{patton2022hybridization}; 
empirical evidence confirms that, even in highly rugged multimodal landscapes, high-fitness peaks effectively guide population evolution~\cite{papkou2023rugged}.
In combinatorial optimization and scheduling, encoding schemes and neighborhood structures shape landscape properties and search performance, supporting algorithm selection and parameter tuning~\cite{tsogbetse2024influence}.
In multi-objective scheduling, landscape-based models substantially improve distributed heterogeneous flexible job-shop scheduling optimization~\cite{zhao2025multi}.
Multi-funnel structures in multi-objective solution spaces have been systematically revealed, informing structural design and performance evaluation of multi-objective optimization algorithms~\cite{ochoa2024funnels}.
The accuracy and representativeness of landscape analysis are strongly influenced by sampling strategies.
It has been empirically demonstrated that different discrete sampling methods, such as Latin hypercube sampling (LHS), significantly affect the representativeness of fitness landscape analysis outcomes~\cite{uher2023impact}.
Structural indicators, such as landscape state values, have been employed to improve the initialization and search performance of swarm intelligence algorithms ~\cite{diao2024enhanced}.
These advances offer valuable references for algorithm design in the present work.

Recent studies have employed fitness landscape analysis to investigate the distributional characteristics of key nodes in social networks, providing theoretical support for the efficient solution of the IM problem~\cite{RN13}.
Accordingly, fitness landscape techniques reveal solution distribution patterns in the search space, enhancing algorithmic perception of structural features and enabling adaptive adjustment, thereby supporting efficient optimization of the influence maximization problem.
\section{Preliminaries}
\label{sec:prelim}
\subsection{Influence Maximization Problem}
A social network is typically modeled as an undirected graph $G = (V, E)$, where $V$ denotes the set of nodes, namely users, and $E$ denotes the set of edges, namely social relationships among users.
The task of influence maximization is to select $k$ nodes as the seed set $S \subseteq V$ on the social network graph $G$ to maximize the expected number of nodes activated during the information diffusion process.
Formally, the influence maximization problem is defined in Equation~\eqref{eq:IM_problem}:
\begin{equation}
	S^* = \mathop{\arg\max}\limits_{S\subseteq V,\; |S|=k} \sigma(S)
	\label{eq:IM_problem}
\end{equation}
where $\sigma(S)$ denotes the expected influence spread of the seed set $S$, computed according to the diffusion model introduced below.
\subsection{Diffusion Model}
The Independent Cascade model serves as a fundamental information diffusion model in social network analysis.
In this model, each activated node attempts to influence every inactive neighbor $v$ independently with probability $p_{uv}$, and only once.
Each propagation attempt is independent, and diffusion iterates until no further activations occur.
\subsection{Expected Diffusion Value}
Given a seed set~$S$, the Expected Diffusion Value (EDV)~\cite{jiang2011simulated} efficiently estimates the expected number of activated nodes in the network.
EDV approximates fitness evaluation in influence maximization by analyzing the one-hop neighborhood structure and the number of connecting edges, which allows the method to avoid costly Monte Carlo simulations.
The EDV function is defined as shown in Equation~\eqref{eq:edv}:
\begin{equation}
	\mathrm{EDV}(S) = k + \sum_{i \in N^{(1)}(S) \setminus S} \left[ 1 - (1-p)^{\tau(i)} \right]
	\label{eq:edv}
\end{equation}

where $k$ is the number of seed nodes, $N^{(1)}(S)$ denotes the set of first-hop neighbors of $S$, $\tau(i)$ is the number of edges between node $i$ and the seed set $S$, and $p$ is the propagation probability.
Essentially, EDV provides an efficient and approximate estimation of influence spread, and has been proven to be both monotone and submodular, which allows for its direct application in greedy and meta-heuristic algorithms~\cite{biswas2022two}.
\subsection{Fitness Landscape}
A fitness landscape is modeled by a triplet $(X, N, f)$~\cite{stadler2002fitness}, where $X$ denotes the set of all feasible solutions, $N$ describes the neighborhood structure of solutions, and $f$ is the fitness function.
This modeling framework comprehensively characterizes the structure of the solution space and the distribution of fitness values that provides a theoretical foundation for the analysis and understanding of optimization algorithms.

The landscape state value~\cite{diao2024enhanced} further characterizes the distribution of individuals within the search space and reflects different evolutionary states of the population.
Specifically, let the population size be $NP$ and the dimension of individuals be $D$.
The average distance of the $i$-th individual is computed using Equation~\eqref{eq:avg_distance}:
\begin{equation}
	d_i = \frac{1}{NP - 1} \sum_{\substack{j=1 \\ j \neq i}}^{NP} \sqrt{ \sum_{k=1}^{D} (X_{i,k} - X_{j,k})^2 }
	\label{eq:avg_distance}
\end{equation}
where $X_{i,k}$ denotes the $k$-th component of individual $i$.
Let $d_g$ denote the distance of the best individual, and $d_{\max}$ and $d_{\min}$ denote the maximum and minimum values among all $d_i$, respectively.
The landscape state value $\lambda$ is defined in Equation~\eqref{eq:landscape_state}:
\begin{equation}
	\lambda = \frac{d_g - d_{min}}{d_{max} - d_{min}}
	\label{eq:landscape_state}
\end{equation}
$\lambda$ takes values in the range $[0, 1]$, which can be used to categorize search behavior into states such as convergence, exploitation, exploration, and escape.
\section{Proposed Algorithm}
\label{sec:implementation}
This section provides a detailed description of the LADE algorithm. LADE incorporates landscape state modeling and an adaptive operator scheduling mechanism, enabling the search process to dynamically perceive structural characteristics of the solution space and flexibly adjust the balance between global exploration and local exploitation in response to changes in population distribution during evolution.The core framework is illustrated in Fig.~\ref{fig:framework}. 
Initially, the hybrid initialization module combines Latin hypercube sampling with degree-centrality-based heuristic sampling, effectively generating an initial population with both high quality and diversity, thereby establishing a solid foundation for subsequent search.
Subsequently, the landscape state perception module employs the Spread Difference Index (SDI) to dynamically compute the landscape state value $\lambda$, facilitating real-time perception of the structural properties of the search space.
Based on $\lambda$, the state-driven operator scheduling module partitions the search process into four distinct states and adaptively selects differential evolution operators, allowing flexible switching between global exploration and local exploitation.
The parameter adaptation module dynamically adjusts the crossover probability (CR) and scaling factor (F) according to search feedback.
On this basis, the local search module conducts neighborhood refinement around current high-quality solutions, continuously improving solution quality.

The following subsections present detailed descriptions of the design and implementation of each key module in LADE.
\begin{figure}[htpb]
	\centering
	\includegraphics[width=1.0\textwidth]{figures/framework.pdf}
	\caption{framework of the LADE.}
	\label{fig:framework}
\end{figure}

\subsection{Sampling Method}
A hybrid initialization strategy that integrates Latin Hypercube Sampling and degree-centrality-based heuristic sampling is utilized in LADE to generate an initial population with both high quality and diversity.
\subsubsection{Latin Hypercube Sampling}
LHS is a stratified sampling technique designed to generate uniformly distributed samples in high-dimensional spaces.
In this study, the network space is treated as a $d$-dimensional sampling space, where each dimension corresponds to a distinct network region.

\textbf{Network Region Partitioning.}
The network $G=(V,E)$ is partitioned into $d$ regions, $R = \{R_0, R_1, \ldots, R_{d-1}\}$, based on the shortest-path distance to the diameter path $P$ (see Equation~\eqref{eq:region_partition}):
\begin{equation}
	R_i = \left\{ v \in V : \min_{u \in P} d(v,u) = i \right\}
	\label{eq:region_partition}
\end{equation}
where $d(v,u)$ denotes the shortest-path length between nodes $v$ and $u$. Each region $R_j$ serves as one dimension in the LHS sampling space.

\textbf{LHS Sampling Procedure:}
Given sample size $N_s$, each sample is represented as $X^{(i)} = [x_1^{(i)}, x_2^{(i)}, \ldots, x_d^{(i)}]$, where $x_j^{(i)}$ indicates the sampling position in region $R_j$ for the $i$-th sample. The procedure consists of the following steps:
\begin{enumerate}[label=(\arabic*)]
	\item For each dimension $j=1,\ldots,d$, generate a random permutation $\pi_j$ of $\{1,\ldots,N_s\}$.
	\item For each sample $i=1,\ldots,N_s$, generate a uniform random variable $U_j^{(i)} \sim \mathrm{Uniform}(0,1)$.
	\item Compute the normalized sampling position:
	\begin{equation}
		z_j^{(i)} = \frac{\pi_j(i) - U_j^{(i)}}{N_s}
	\end{equation}
	\item Convert the continuous position to a node index:
	\begin{equation}
		\mathrm{idx}_j^{(i)} = \left\lfloor z_j^{(i)} \cdot |R_j| \right\rfloor
	\end{equation}
	where $|R_j|$ is the number of nodes in region $R_j$, and $\lfloor \cdot \rfloor$ denotes the floor operation.
	\item Select the corresponding node:
	\begin{equation}
		v_j^{(i)} = \mathrm{list}(R_j)[\mathrm{idx}_j^{(i)}]
	\end{equation}
	where $\mathrm{list}(R_j)$ denotes the ordered list of nodes in $R_j$.
	\item Construct the sampled solution:
	\begin{equation}
		S^{(i)} = \{ v_1^{(i)}, v_2^{(i)}, \ldots, v_d^{(i)} \}
	\end{equation}
\end{enumerate}

\textbf{Three-Stage Supplementation Mechanism.}
If the number of sampled nodes is less than $k$, a three-stage supplementation mechanism is applied.
First, one node with the highest combined centrality is selected from each region not yet covered by the current sample (one node per region), until the number of selected nodes reaches $k$ or all regions have been considered.
If the total remains insufficient, nodes with the highest combined centrality are selected from the set of bridge nodes.
If the number of nodes is still below $k$, the remaining slots are filled with nodes that are not assigned to any region and have not yet been selected (i.e., peripheral nodes), ranked in descending order of combined centrality.
The combined centrality is defined as the normalized weighted sum of degree centrality and betweenness centrality.
\subsubsection{Degree Centrality Sampling}
Given a seed set size $k$ and the required number of samples $N_s$, degree centrality sampling first selects the $k$ nodes with the highest degrees as the initial solution. Then, for each node, with a probability of 0.5, it is replaced by a randomly selected node not present in the current solution. This sampling process is repeated until $N_s$ distinct solutions are obtained. Finally, representative and diverse solutions are filtered from the union of degree centrality and Latin hypercube sampling results to initialize the population.

\subsection{Initialization}
In the initialization phase, a hybrid sampling strategy is adopted.
Specifically, candidate solutions are first generated separately using Latin Hypercube Sampling and a degree centrality-based heuristic.
The former ensures uniform coverage of the solution space, while the latter emphasizes the selection of locally high-quality node combinations.
Subsequently, the two sets of candidate solutions are merged.
The top-performing individuals from each sampling method are selected according to their fitness values and then combined into a high-quality solution set.
To ensure structural diversity, redundant solutions with high structural similarity are filtered out based on the Jaccard similarity coefficient.
If the resulting population size is insufficient, additional high-fitness solutions are supplemented preferentially.
If there is still a shortage, new solutions are dynamically generated by combining bridge nodes and ordinary nodes.
Ultimately, this process produces an initial population with both high fitness and structural diversity.
The pseudocode is presented in Algorithm~\ref{alg:hybrid_initialization}.
\begin{algorithm}[htbp]
	\caption{Hybrid Initialization}
	\label{alg:hybrid_initialization}
	\begin{algorithmic}[1]
		\Require lhsSolutions, scoreSolutions, pop, qp, div, bridgeNodes, $G$, $p$, $k$
		\Ensure Initial population $P$
		\State $allSolutions \gets lhsSolutions \cup scoreSolutions$
		\State $fitness \gets \mathrm{EDV}(allSolutions, G, p)$
		\State $qualNum \gets \lfloor pop \times qp \rfloor$
		\State $lhsQuality \gets \mathrm{qualityFilter}(lhsSolutions, fitness, qualNum)$
		\State $scoreQuality \gets \mathrm{qualityFilter}(scoreSolutions, fitness, qualNum)$
		\State $combined \gets lhsQuality \cup scoreQuality$
		\State $init \gets \mathrm{DiversityFilter}(combined, div)$
		\State $remain \gets allSolutions \setminus init$
		\State $sorted \gets$ $remain$, sorted by $fitness$ descending
		
		\While{$|init| < pop$}
		\If{$sorted$ not empty}
		\State $sol \gets \mathrm{Next}(sorted)$
		\State $init.\mathrm{append}(sol)$
		\Else
		\State $b \gets \mathrm{randInt}(1, \min(k,~|\mathrm{bridgeNodes}|+1))$
		\State $bridgeSample \gets \mathrm{randSample}(\mathrm{bridgeNodes},~b)$
		\State $nonBridgeNodes \gets V(G) \setminus \mathrm{bridgeNodes}$
		\State $nonBridgeSample \gets \mathrm{randSample}(nonBridgeNodes,~k-b)$
		\State $newSol \gets bridgeSample \cup nonBridgeSample$
		\State $init.\mathrm{append}(newSol)$
		\EndIf
		\EndWhile
		
		\State $P \gets \mathrm{RemoveDuplicates}(init)$
		\State \Return $P[:pop]$
	\end{algorithmic}
\end{algorithm}

\subsection{Landscape State Perception Mechanism}

To accommodate complex variations in the search space, a landscape state perception mechanism is introduced. This mechanism enables real-time identification of the population’s distribution over the fitness landscape and supports dynamic strategy adjustment based on evolutionary states, thereby enhancing structural adaptability and avoiding stagnation near local optima.

\subsubsection{Landscape State Value Computation}

The concept of a landscape state value has been proposed to characterize the distribution of individuals in the fitness landscape and to guide adaptive parameter control~\cite{RN13}. Drawing on this idea, a landscape state value is defined to dynamically represent population distribution, serving as the central indicator for strategy scheduling.

For any two solutions \( S_1 \) and \( S_2 \), the \textit{Spread Diversity Index} (SDI) is defined as follows in Equation~\eqref{eq:spread_diversity_index}:
\begin{equation}
	\mathrm{SDI}(S_1, S_2) = \frac{\sum_{v \in S_1 \triangle S_2} \mathrm{LFV}(v)}{\sum_{v \in S_1 \cup S_2} \mathrm{LFV}(v)}
	\label{eq:spread_diversity_index}
\end{equation}
where $S_1 \triangle S_2$ denotes the symmetric difference of the two solutions, and $\mathrm{LFV}(v)$ represents the local influence value of node $v$~\cite{qiu2019lgim}.

Given a population $P = \{ S_1, S_2, \dots, S_{N_p} \}$, the average SDI for an individual $S_i$ is computed according to Equation~\eqref{eq:avg_sdi}:
\begin{equation}
	\overline{\mathrm{SDI}}_i = \frac{1}{N_p - 1} \sum_{\substack{j=1 \\ j \neq i}}^{N_p} \mathrm{SDI}(S_i, S_j)
	\label{eq:avg_sdi}
\end{equation}

Let $g$ denote the current best individual, and let $\overline{\mathrm{SDI}}_g$ represent its average SDI. Denote the minimum and maximum average SDI values in the population as $\mathrm{SDI}_{\min}$ and $\mathrm{SDI}_{\max}$, respectively. The landscape state value $\lambda$ is calculated according to Equation~\eqref{eq:lambda_calculation}:
\begin{equation}
	\lambda = \frac{\overline{\mathrm{SDI}}_g - \mathrm{SDI}_{\min}}{\mathrm{SDI}_{\max} - \mathrm{SDI}_{\min}}
	\label{eq:lambda_calculation}
\end{equation}

\subsubsection{Landscape State Determination}

To enable dynamic monitoring of population distribution, the landscape state value $\lambda$ is recorded at each generation. The most recent ten values of $\lambda$ are used to calculate the 25th and 75th percentiles, denoted as $Q_1$ and $Q_3$, respectively. Based on the value of $\lambda$, the population is classified into one of four evolutionary states, as defined in Equation~\eqref{eq:evolutionary_states}:
\begin{equation}
	\text{State} =
	\begin{cases}
		\text{Convergence}, & \lambda \in (0, Q_1] \\
		\text{Exploitation}, & \lambda \in \left( Q_1, \frac{Q_1 + Q_3}{2} \right] \\
		\text{Exploration}, & \lambda \in \left( \frac{Q_1 + Q_3}{2}, Q_3 \right] \\
		\text{Escape}, & \lambda \in (Q_3, 1] \text{ or } (\lambda \approx 0 \text{ and fitness stagnation occurs})
	\end{cases}
	\label{eq:evolutionary_states}
\end{equation}

This mechanism enables real-time monitoring of the population distribution to dynamically identify situations where the search may become trapped in local optima.
A large value of $\lambda$ signals that the population is primarily distributed in isolated regions far from the current best solution, whereas $\lambda \approx 0$ together with prolonged stagnation in fitness indicates pronounced concentration and search stagnation.
In response, interval-triggered and condition-triggered escape activation mechanisms are designed to initiate strategies for escaping local optima under scenarios of either significant dispersion or convergence stagnation.
The detected population state subsequently informs the adaptive adjustment of mutation operators.
\subsection{State-Driven Differential Evolution}
\subsubsection{Adaptive Parameter Generation}

To adapt to dynamic changes in population states during the search process, an adaptive parameter mechanism is introduced~\cite{biswas2022two}. In each generation, the scaling factor $F_{i}$ and the crossover probability $Cr_{i}$ for each individual are generated as follows:
\begin{align}
	F_{i}\  &\sim\  \mathrm{Cauchy}(\mu_{F},\, 0.1),\quad F_{i} \in [0,\, 1], \\
	Cr_{i}\ &\sim\  \mathcal{N}(\mu_{Cr},\, 0.1),\quad Cr_{i} \in [0,\, 1].
\end{align}




During evolution, parameter values of successful individuals in each generation are stored in a historical memory of length $M$. Exponential smoothing is then performed on the mean values of the parameters using this historical information:
\begin{align}
	\mu_{Cr} &= (1-c)\mu_{Cr} + c \cdot \mathrm{mean}_{A}(S_{Cr}), \\
	\mu_{F} &= (1-c)\mu_{F} + c \cdot \mathrm{mean}_{L}(S_{F}),
\end{align}
where $S_{Cr}$ and $S_{F}$ denote the sets of successful $Cr$ and $F$ values in memory, and $c$ is the smoothing coefficient. This mechanism adaptively adjusts parameter distributions to improve algorithm adaptability and search performance.
