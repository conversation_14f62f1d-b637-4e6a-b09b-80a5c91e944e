import time
from LIE import *  # 有LIE_two_hop 函数
from utils import *  # 有solution_similarity quality_filter diversity_filter函数
from anfde import *  # 有ANFDE类，主要是涉及后面的物种形成的种群和拥挤种群的CR和F参数的独立
from graph import *  # 有read_graph_from_file
from IC import *  # 有 mc_influence
from sample_solutions import *  # 有sample_lhs sample_score
from utils import cached_LIE_two_hop  # 导入缓存版的LIE_two_hop函数
import networkx as nx
import datetime
import os
import numpy as np
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp
from functools import lru_cache, wraps
import hashlib
import math

# ==================1.初始化函数 (initialize_population_hybrid) - ================

"""使用LHS解和评分解初始化种群，同时进行质量和多样性筛选"""
def initialize_population_hybrid(
        G,
        lhs_solutions,
        score_solutions,
        population_size,
        p,
        quality_proportion,
        similarity_threshold,
        bridge_nodes
):
    """"""
    """
    使用LHS解和评分解初始化种群，同时进行质量和多样性筛选
    :param G:
    :param lhs_solutions: 通过拉丁超立方采样生成的初始解集合。
    :param score_solutions: 度+扰动生成的采样解集合。
    :param population_size: 种群目标大小。
    :param p: 激活概率
    :param quality_proportion:  种群中高质量解的比例。
    :param similarity_threshold: 用于过滤相似解的阈值。
    :param bridge_nodes: 网络图中识别出的桥节点。
    :return:
    """

    print("\n" + "=" * 30 + " 初始化阶段开始 " + "=" * 30)

    # ================== 阶段0: 并行缓存所有候选解的适应度值 =================
    print("\n[阶段0] 并行预计算所有候选解的适应度值...")

    # 合并所有候选解
    all_solutions = lhs_solutions + score_solutions

    # 使用进程池以提高计算密集型任务性能
    fitness_cache = {}
    
    # 动态调整进程数和批处理大小
    cpu_count = mp.cpu_count()
    workers = max(1, min(cpu_count - 1, 8))  # 保留一个核心给系统，最多使用8个核心
    
    # 优化批处理大小: 根据解的数量和CPU核心数动态调整
    solutions_per_worker = math.ceil(len(all_solutions) / workers)
    batch_size = max(10, min(solutions_per_worker, 50))  # 确保每批至少10个，最多50个解
    
    batches = [all_solutions[i:i + batch_size] for i in range(0, len(all_solutions), batch_size)]
    
    print(f"使用 {workers} 个进程处理 {len(batches)} 批次数据 (每批 ~{batch_size} 个解)")

    # 重新定义函数，避免闭包引用外部变量
    def compute_fitness_batch_thread(solutions_batch):
        return {tuple(sol): cached_LIE_two_hop(sol, G, p) for sol in solutions_batch}
    
    # 直接使用进程池，不考虑图大小
    try:
        # 为进程池准备一个可序列化的函数
        def compute_fitness_batch_process(args):
            import pickle
            solutions_batch, graph_pickle, prob = args
            # 反序列化图对象
            local_G = pickle.loads(graph_pickle)
            results = {}
            for sol in solutions_batch:
                # 直接调用函数而非缓存版本，避免跨进程缓存问题
                results[tuple(sol)] = LIE_two_hop(sol, local_G, prob)
            return results
        
        # 序列化图对象一次，所有工作进程共享
        import pickle
        graph_pickle = pickle.dumps(G)
        
        # 准备进程池的参数
        process_args = [(batch, graph_pickle, p) for batch in batches]
        
        print("使用进程池进行计算...")
        with ProcessPoolExecutor(max_workers=workers) as executor:
            batch_results = list(executor.map(compute_fitness_batch_process, process_args))
            for batch_cache in batch_results:
                fitness_cache.update(batch_cache)
        print("进程池计算成功完成")
        
    except Exception as e:
        print(f"进程池执行出错: {e}, 降级为线程池...")
        # 降级为线程池作为备选方案
        with ThreadPoolExecutor(max_workers=workers) as executor:
            batch_results = list(executor.map(compute_fitness_batch_thread, batches))
            for batch_cache in batch_results:
                fitness_cache.update(batch_cache)

    print(f"适应度值缓存完成，共缓存{len(fitness_cache)}个解的适应度值.")

    # ================== 阶段1: 质量筛选 50% ==================
    print(f"\n[阶段1] 质量筛选 (质量比例={quality_proportion})")
    n_quality = int(population_size * quality_proportion)
    print(f"目标保留数: LHS={n_quality}, 评分={n_quality}")

    # LHS质量筛选：使用预计算的缓存
    print("\n[LHS] 原始解数量:", len(lhs_solutions))
    # 从 lhs_solutions中挑选适应度最高的解
    lhs_quality = quality_filter(lhs_solutions, fitness_cache, n_quality)
    print("[LHS] 筛选后保留:", len(lhs_quality))

    # 评分质量筛选：使用预计算的缓存
    print("\n[评分] 原始解数量:", len(score_solutions))
    # 从score_solutions中挑选适应度最高的解
    score_quality = quality_filter(score_solutions, fitness_cache, n_quality)
    print("[评分] 筛选后保留:", len(score_quality))

    # ================== 阶段2: 合并解集 ==================
    combined_solutions = lhs_quality + score_quality
    print(f"\n[阶段2] 合并后解总数: {len(combined_solutions)} (LHS+评分)")

    # ================== 阶段3: 多样性筛选 ==================
    # 确保种群的多样性，以避免过早收敛。
    # 根据 similarity_threshold 筛选合并后的解集，确保相似的解不会进入最终种群。
    print(f"\n[阶段3] 多样性筛选 (阈值={similarity_threshold})")
    
    # 将解集转换为适合快速处理的数据结构
    # 预处理，只进行一次转换，避免重复操作
    combined_sets = []
    combined_sets_lengths = []
    
    # 预计算所有解的集合形式和长度
    for solution in combined_solutions:
        solution_set = set(solution)
        combined_sets.append(solution_set)
        combined_sets_lengths.append(len(solution_set))
    
    # 提前创建结果列表，避免频繁追加操作
    initial_population = []
    excluded_indices = set()  # 使用集合记录排除的索引
    
    # 主循环：贪婪选择多样性解集
    for i in range(len(combined_solutions)):
        if i in excluded_indices:
            continue  # 已被排除，跳过
        
        # 添加当前解
        initial_population.append(combined_solutions[i])
        current_set = combined_sets[i]
        current_len = combined_sets_lengths[i]
        
        # 标记与当前解相似的其他解
        for j in range(i+1, len(combined_solutions)):
            if j in excluded_indices:
                continue  # 已被排除，跳过
            
            # 快速计算Jaccard相似度
            other_set = combined_sets[j]
            other_len = combined_sets_lengths[j]
            
            # 计算交集大小
            intersection_size = len(current_set.intersection(other_set))
            # 计算并集大小 (直接用长度和减交集，避免计算并集)
            union_size = current_len + other_len - intersection_size
            
            # 计算Jaccard相似度
            similarity = intersection_size / union_size if union_size > 0 else 0
            
            # 如果相似度高于阈值，排除该解
            if similarity > similarity_threshold:
                excluded_indices.add(j)
    
    print(f"筛选后种群大小: {len(initial_population)} (目标: {population_size})")

    # ================== 阶段 4: 补充解 ==================
    print("\n[阶段4] 补充解过程开始")
    
    # 优化：将列表转换为元组集合，只做一次转换
    all_candidate_tuples = set()
    for sol in lhs_solutions + score_solutions:
        all_candidate_tuples.add(tuple(sorted(sol)))
        
    # 对已选解也只做一次转换
    selected_tuples = set()
    for sol in initial_population:
        selected_tuples.add(tuple(sorted(sol)))
    
    # 计算剩余解
    remaining_solutions = all_candidate_tuples - selected_tuples
    
    print(f"剩余候选解: {len(remaining_solutions)}")

    # 优化：预计算所有剩余解的适应度并排序，避免每次循环都查找最大值
    if remaining_solutions:
        # 检查哪些解需要计算适应度
        uncached_solutions = [sol for sol in remaining_solutions if sol not in fitness_cache]
        
        if uncached_solutions:
            print(f"发现 {len(uncached_solutions)} 个未缓存的解，正在计算适应度...")
            
            # 对未缓存的解计算适应度
            for sol in uncached_solutions:
                # 转换回列表用于适应度计算
                sol_list = list(sol)
                # 计算适应度并添加到缓存
                fitness_cache[sol] = cached_LIE_two_hop(sol_list, G, p)
            
            print(f"完成 {len(uncached_solutions)} 个解的适应度计算")
        
        # 现在所有解都已经在缓存中，进行排序
        remaining_with_fitness = [(sol, fitness_cache[sol]) for sol in remaining_solutions]
        remaining_with_fitness.sort(key=lambda x: x[1], reverse=True)  # 按适应度降序排序
        
        # 将排序后的解转换回列表形式，方便按顺序获取
        sorted_remaining = [sol for sol, _ in remaining_with_fitness]
    else:
        sorted_remaining = []

    iteration = 0
    remaining_index = 0  # 跟踪当前处理的索引
    
    while len(initial_population) < population_size:
        iteration += 1
        print(f"\n--- 补充迭代 {iteration} ---")
        print("当前种群大小:", len(initial_population))
        
        if not remaining_solutions or remaining_index >= len(sorted_remaining):
            # 生成全新解
            print("\n[补充] 生成全新解")
            bridge_sample_size = min(2, len(bridge_nodes))
            non_bridge_size = population_size - bridge_sample_size

            bridge_sample = random.sample(bridge_nodes, bridge_sample_size)
            non_bridge_nodes = [n for n in G.nodes if n not in bridge_nodes]
            non_bridge_sample = random.sample(non_bridge_nodes, non_bridge_size)

            new_sol = bridge_sample + non_bridge_sample
            print(f"桥节点采样: {bridge_sample}")
            print(f"非桥节点采样: {non_bridge_sample}")
            print("生成新解:", new_sol)

            initial_population.append(new_sol)
        else:
            # 直接从排序后的列表获取下一个最优解
            best_remaining = sorted_remaining[remaining_index]
            fitness = fitness_cache[best_remaining]
            print("\n[补充] 选择排序后的最优解")
            print("选择解:", best_remaining)
            print("适应度:", f"{fitness:.4f}")
            
            # 更新索引，移动到下一个解
            remaining_index += 1
            
            # 添加到初始种群
            selected_tuples.add(best_remaining)
            initial_population.append(list(best_remaining))

        print("补充后种群大小:", len(initial_population))

    # ================== 阶段5: 最终处理 ==================
    print("\n[阶段5] 最终处理")
    print("转换前最后一个解示例:", initial_population[-1])

    # 转换为整数节点
    initial_population = [[int(node) for node in individual] for individual in initial_population]
    print("转换后最后一个解示例:", initial_population[-1])

    # 优化去重检查 - 提高内存和速度效率
    # 1. 直接使用集合进行快速去重
    # 2. 维护排序后的元组到原始解的映射
    
    unique_tuples = set()  # 用于快速检查重复
    unique_solutions = []  # 存储最终结果
    tuple_to_original = {}  # 映射排序元组到原始解
    
    for sol in initial_population:
        # 对解进行排序并转换为元组以便哈希
        sol_tuple = tuple(sorted(sol))
        
        # 如果这个排序后的解之前没见过，则添加
        if sol_tuple not in unique_tuples:
            unique_tuples.add(sol_tuple)
            unique_solutions.append(sol)
            tuple_to_original[sol_tuple] = sol
    
    print(f"去重后种群大小: {len(unique_solutions)}")

    print("\n" + "=" * 30 + " 初始化阶段完成 " + "=" * 30)
    # 确保返回不超过population_size的解
    return unique_solutions[:population_size]


# ========================主调用=============================
def ANFDE_IM(G, k, g, pop, FEsMaxs, p, SN, file=None):  # 添加 file 参数
    start_time = time.time()  # 记录函数开始时间
    bridge_nodes = detect_bridge_nodes(G) # 预计算桥节点
    combined_scores = calculate_combined_centrality_igraph(G) # 预计算节点综合评分
    # ========================1. 使用 LHS 和基于度采样生成初始解====================
    lhs_solutions = sample_lhs(G, k, SN // 2, bridge_nodes, combined_scores)
    score_solutions = sample_score(G, k, SN // 2)

    # 1.1 打印 LHS 解及其适应度值
    # print("\n=== LHS 解 ===")
    # for i, solution in enumerate(lhs_solutions):
    #     fitness = cached_LIE_two_hop(solution, G, p)
    #     print(f"LHS解 {i + 1}: 节点集合: {sorted(solution)}, 适应度: {fitness:.4f}")
    # 1.2 打印基于评分的解及其适应度值
    # print("\n=== 基于评分的解 ===")
    # for i, solution in enumerate(score_solutions):
    #     fitness = cached_LIE_two_hop(solution, G, p)
    #     print(f"评分解 {i + 1}: 节点集合: {sorted(solution)}, 适应度: {fitness:.4f}")

    # ------------------2. 初始化种群---------------------------
    initial_population = initialize_population_hybrid(G, lhs_solutions, score_solutions, pop, p,
                                                     quality_proportion=0.5, similarity_threshold=0.8,
                                                     bridge_nodes=bridge_nodes)
    # 5. 打印初始化种群的信息
    print("\n=== 初始化种群 ===")
    for i, solution in enumerate(initial_population):  # 遍历初始种群
        fitness = cached_LIE_two_hop(solution, G, p)
        print(f"初始化种群解 {i + 1}: 节点集合: {sorted(solution)}, 适应度: {fitness:.4f}")

    # 计算初始景观状态值 λ
    # ------------------3. 实例化 ANFDE 类----------------------
    print(f"开始计算初始景观状态值λ：")
    anfde = ANFDE(G, p, bridge_nodes=bridge_nodes, k=k, distance_type='symmetric')  # 实例化 ANFDE 类
    initial_lambda = anfde._compute_lambda(initial_population)
    print(f"计算完成：")
    print(f"开始识别状态：")
    initial_state = anfde._determine_state(initial_lambda, initialization=True)
    print(f"状态识别完毕：")
    print(f"\n初始景观状态值 λ: {initial_lambda:.4f}, 初始状态: {initial_state}")
    # 初始化增强多样性
    if initial_lambda < 0.3:
        print(f"种群多样性不足，需增强：只是提示，先不增强")

        # ------------------4. 运行 ANFDE 算法----------------------
    print("\n" + "=" * 30 + " 开始运行 ANFDE 主循环 " + "=" * 30)
    final_population, fitness_history, lambda_history, state_history, best_individual, best_fitness, running_time \
        = anfde.run(
        initial_population=initial_population,
        k=k, g=g, FEsMaxs=FEsMaxs,
        lhs_solutions=lhs_solutions,  # <--- 传递 LHS 解
        score_solutions=score_solutions,  # <--- 传递评分解

        )
    print("\n" + "=" * 30 + " ANFDE 主循环结束 " + "=" * 30)

    # 5. 打印结果  (关键修改：处理输出格式)
    print("\n最终景观状态值 λ 历史:")
    print([float(x) for x in lambda_history])  # 将numpy.float64转换为float
    print("\n最终状态历史:")
    print(state_history)
    print("\n适应度历史:")
    print([float(x) for x in fitness_history])  # 将numpy.float64转换为float

    # 保存结果图表（包括矢量图）
    print("\n保存结果图表...")
    saved_files = anfde.plot_results(save_vector=True)
    print(f"已保存图表文件: {saved_files}")

    return final_population, fitness_history, anfde  # 返回anfde实例


def run_performance_test():
    """运行性能测试，比较优化前后的性能"""
    print("=" * 60)
    print("ANFDE-IM 算法性能测试")
    print("=" * 60)

    # 测试不同规模的网络
    test_networks = [
        ("networks/karate.txt", "Karate Club"),
        ("networks/blog-int.txt", "Blog Network"),
        # 可以添加更多网络进行测试
    ]

    for file_path, network_name in test_networks:
        print(f"\n{'='*20} 测试网络: {network_name} {'='*20}")

        try:
            # 加载网络
            G = gen_graph(file_path)
            isolates = list(nx.isolates(G))
            G.remove_nodes_from(isolates)

            print(f"网络规模: {G.number_of_nodes()} 节点, {G.number_of_edges()} 边")

            # 测试参数
            k = min(10, G.number_of_nodes() // 10)  # 自适应种子集大小
            p = 0.05
            pop = 20  # 较小的种群用于快速测试
            g = 50    # 较少的迭代次数
            FEsMaxs = 1000 * k
            SN = 100

            print(f"测试参数: k={k}, pop={pop}, g={g}, FEsMaxs={FEsMaxs}")

            # 运行算法
            start_time = time.time()
            final_population, final_fitness, anfde = ANFDE_IM(G, k, g, pop, FEsMaxs, p, SN, file=file_path)
            end_time = time.time()

            print(f"运行时间: {end_time - start_time:.2f} 秒")
            print(f"最终最佳适应度: {max(final_fitness):.4f}")

        except Exception as e:
            print(f"测试 {network_name} 时出错: {e}")
            continue

if __name__ == '__main__':
    # 选择运行模式
    # mode = input("选择运行模式 (1: 标准运行, 2: 性能测试): ").strip()
    mode = 1

    if mode == "2":
        run_performance_test()
    else:
        # ==================== 1. 数据加载与预处理 ================
        # file = "networks/netscience-int.txt"
        # file = "networks/AS733.txt"
        # file = "networks/lastfm.txt"
        # file = "networks/twin_zero_indexed.txt"
        # file = "networks/NetHEHT.txt"
        file = "networks/deezer.txt"
        # file = "networks/er.txt"
        # file = "networks/ws.txt"
        # file = "networks/ba.txt"


        G = gen_graph(file)  # 假设该函数返回 networkx.Graph 对象

        # 移除孤立节点
        isolates = list(nx.isolates(G))
        G.remove_nodes_from(isolates)
        print(f"移除孤立节点数量: {len(isolates)}")
        print(f"剩余节点数: {G.number_of_nodes()}, 边数: {G.number_of_edges()}")

        # ==================== 2. 参数设置 ====================
        ks = [50, 60, 70, 80, 90, 100]  # 种子集大小
        for k in ks:
            p = 0.05  # 激活概率
            pop = 30  # 种群大小
            g = 200  # 迭代次数
            FEsMaxs = 5000 * k  # 最大函数评估次数
            SN = 500

            print(f"\n开始运行 ANFDE-IM 算法 (优化版)")
            print(f"参数设置: k={k}, pop={pop}, g={g}, FEsMaxs={FEsMaxs}, SN={SN}")

            start_time = time.time()
            final_population, final_fitness, anfde = ANFDE_IM(G, k, g, pop, FEsMaxs, p, SN, file=file)
            end_time = time.time()

            print(f"\n算法运行完成!")
            print(f"总运行时间: {end_time - start_time:.2f} 秒")
            print(f"最终最佳适应度: {max(final_fitness):.4f}")
            print(f"使用CPU核心数: {mp.cpu_count()}")
            print("优化特性: 并行计算、向量化操作、智能缓存、矢量图输出")
            
            # 打印局部搜索统计信息
            ls_stats = anfde.get_local_search_stats()
            print("\n=== 局部搜索成功率统计 ===")
            print(f"整体局部搜索: {ls_stats['overall']['successes']}/{ls_stats['overall']['attempts']} = {ls_stats['overall']['rate']:.2f}%")
            print(f"种群改进搜索: {ls_stats['spart']['successes']}/{ls_stats['spart']['attempts']} = {ls_stats['spart']['rate']:.2f}%")
            print(f"全局最优搜索: {ls_stats['gbest']['successes']}/{ls_stats['gbest']['attempts']} = {ls_stats['gbest']['rate']:.2f}%")



