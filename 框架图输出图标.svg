<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .output-box { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; }
      .seed-node { fill: #66bb6a; stroke: #2e7d32; stroke-width: 1.5; }
      .best-seed { fill: #4caf50; stroke: #1b5e20; stroke-width: 2; }
      .connection { stroke: #81c784; stroke-width: 1; opacity: 0.8; }
      .success-icon { fill: #4caf50; }
      .text { font-family: Arial, sans-serif; font-size: 10px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
      .small-text { font-family: Arial, sans-serif; font-size: 7px; text-anchor: middle; fill: #4caf50; }
    </style>
    <filter id="output-glow">
      <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 输出容器 -->
  <rect x="10" y="20" width="100" height="50" rx="8" class="output-box" filter="url(#output-glow)"/>
  
  <!-- 种子节点网络 -->
  <circle cx="25" cy="35" r="4" class="seed-node"/>
  <circle cx="45" cy="30" r="5" class="best-seed"/>
  <circle cx="65" cy="35" r="4" class="seed-node"/>
  <circle cx="85" cy="40" r="4" class="seed-node"/>
  <circle cx="95" cy="55" r="3" class="seed-node"/>
  
  <!-- 连接线 -->
  <line x1="25" y1="35" x2="45" y2="30" class="connection"/>
  <line x1="45" y1="30" x2="65" y2="35" class="connection"/>
  <line x1="65" y1="35" x2="85" y2="40" class="connection"/>
  <line x1="85" y1="40" x2="95" y2="55" class="connection"/>
  <line x1="25" y1="35" x2="85" y2="40" class="connection"/>
  
  <!-- 成功标识 -->
  <g transform="translate(95,25)">
    <circle cx="0" cy="0" r="6" class="success-icon"/>
    <path d="M -2,-1 L 0,2 L 3,-2" fill="none" stroke="#fff" stroke-width="1.5" stroke-linecap="round"/>
  </g>
  
  <!-- 输出标识 -->
  <g transform="translate(15,25)">
    <rect x="0" y="0" width="8" height="6" rx="1" fill="#ffd700" stroke="#ff8f00" stroke-width="0.5"/>
    <text x="4" y="4" style="font-size:4px; text-anchor:middle; fill:#ff8f00;">OUT</text>
  </g>
  
  <!-- 标题 -->
  <text x="60" y="12" class="text">🏆 最优种子集</text>
  <text x="60" y="78" class="small-text">算法收敛输出</text>
</svg>
