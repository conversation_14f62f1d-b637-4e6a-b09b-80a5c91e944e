<?xml version="1.0" encoding="UTF-8"?>
<svg width="160" height="120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .finish-line { fill: #fff; stroke: #4caf50; stroke-width: 3; }
      .trophy { fill: #ffd700; stroke: #ff8f00; stroke-width: 2; }
      .trophy-base { fill: #8d6e63; stroke: #5d4037; stroke-width: 1; }
      .seed-output { fill: #66bb6a; stroke: #2e7d32; stroke-width: 1.5; }
      .best-seed { fill: #4caf50; stroke: #1b5e20; stroke-width: 2; }
      .output-stream { stroke: #81c784; stroke-width: 2; opacity: 0.8; }
      .success-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; opacity: 0.6; }
      .text { font-family: Arial, sans-serif; font-size: 11px; font-weight: bold; text-anchor: middle; fill: #2e7d32; }
      .small-text { font-family: Arial, sans-serif; font-size: 7px; text-anchor: middle; fill: #4caf50; }
      .result-text { font-family: Arial, sans-serif; font-size: 8px; text-anchor: middle; fill: #1b5e20; }
    </style>
    <filter id="finish-glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 成功背景 -->
  <rect x="10" y="20" width="140" height="80" rx="10" class="success-bg" filter="url(#finish-glow)"/>
  
  <!-- 终点线 -->
  <rect x="15" y="25" width="4" height="70" fill="#4caf50"/>
  <rect x="25" y="25" width="4" height="70" fill="#4caf50"/>
  <text x="35" y="35" style="font-size:8px; fill:#4caf50; font-weight:bold;">FINISH</text>
  
  <!-- 奖杯 -->
  <g transform="translate(70,45)">
    <!-- 奖杯杯身 -->
    <ellipse cx="0" cy="0" rx="12" ry="8" class="trophy"/>
    <!-- 奖杯把手 -->
    <ellipse cx="-15" cy="0" rx="3" ry="6" fill="none" stroke="#ff8f00" stroke-width="2"/>
    <ellipse cx="15" cy="0" rx="3" ry="6" fill="none" stroke="#ff8f00" stroke-width="2"/>
    <!-- 奖杯底座 -->
    <rect x="-8" y="8" width="16" height="6" rx="2" class="trophy-base"/>
    <rect x="-6" y="14" width="12" height="4" rx="2" class="trophy-base"/>
    <!-- 奖杯装饰 -->
    <text x="0" y="3" style="font-size:6px; text-anchor:middle; fill:#ff8f00; font-weight:bold;">★</text>
  </g>
  
  <!-- 输出的种子流 -->
  <g transform="translate(100,50)">
    <!-- 输出管道 -->
    <rect x="0" y="-3" width="25" height="6" rx="3" fill="#81c784" opacity="0.5"/>
    
    <!-- 流出的种子 -->
    <circle cx="30" cy="-8" r="3" class="seed-output"/>
    <circle cx="38" cy="0" r="4" class="best-seed"/>
    <circle cx="46" cy="8" r="3" class="seed-output"/>
    <circle cx="35" cy="12" r="2.5" class="seed-output"/>
    <circle cx="42" cy="-12" r="2.5" class="seed-output"/>
    
    <!-- 连接线显示网络结构 -->
    <line x1="30" y1="-8" x2="38" y2="0" class="output-stream"/>
    <line x1="38" y1="0" x2="46" y2="8" class="output-stream"/>
    <line x1="38" y1="0" x2="35" y2="12" class="output-stream"/>
    <line x1="38" y1="0" x2="42" y2="-12" class="output-stream"/>
  </g>
  
  <!-- 成功标识 -->
  <g transform="translate(130,30)">
    <circle cx="0" cy="0" r="8" fill="#4caf50"/>
    <path d="M -3,-1 L -1,3 L 4,-3" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round"/>
  </g>
  
  <!-- 算法状态指示 -->
  <g transform="translate(50,70)">
    <rect x="0" y="0" width="60" height="12" rx="6" fill="#c8e6c9" stroke="#4caf50" stroke-width="1"/>
    <text x="30" y="8" class="result-text">收敛完成 ✓ 输出就绪</text>
  </g>
  
  <!-- 性能指标 -->
  <g transform="translate(20,85)">
    <text x="0" y="0" class="small-text">影响力: 95.2%</text>
    <text x="50" y="0" class="small-text">种子数: 5</text>
    <text x="90" y="0" class="small-text">迭代: 150</text>
  </g>
  
  <!-- 标题 -->
  <text x="80" y="15" class="text">🏁 算法完成输出</text>
  
  <!-- 输出箭头 -->
  <defs>
    <marker id="final-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#2e7d32"/>
    </marker>
  </defs>
  
  <path d="M 95 50 L 125 50" fill="none" stroke="#2e7d32" stroke-width="3" marker-end="url(#final-arrow)"/>
</svg>
