\documentclass[12pt]{article}
\usepackage[utf8]{inputenc}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{xeCJK}
\usepackage{geometry}
\geometry{a4paper,margin=2.5cm}

\title{拉丁超立方采样（LHS）在影响力最大化中的数学公式详细梳理}
\author{研究团队}
\date{\today}

\begin{document}

\maketitle

\section{LHS采样的基本概念}

拉丁超立方采样（Latin Hypercube Sampling, LHS）是一种分层采样方法，其核心思想是将采样空间划分为若干个子区域，然后在每个子区域中进行采样，确保样本在整个空间中均匀分布。

在影响力最大化问题中，我们需要将这个概念适配到网络拓扑结构上。

\section{LHS采样的数学公式推导}

\subsection{第一步：网络空间划分}

首先，我们需要将网络$G(V,E)$划分为$m$个区域。基于网络直径路径进行距离分层：

\begin{equation}
\text{Region}_i = \{v \in V : \min_{u \in \text{diameter\_path}} d(v,u) = i\}, \quad i = 0, 1, 2, \ldots, m-1
\end{equation}

其中：
- $\text{diameter\_path}$是网络的直径路径
- $d(v,u)$是节点$v$到节点$u$的最短路径距离
- $m$是网络的最大距离层数

\subsection{第二步：确定采样参数}

设我们要生成$n$个种子集，每个种子集包含$k$个节点。

\textbf{总采样点数：}
\begin{equation}
N_{total} = n \times k
\end{equation}

\textbf{每个区域的理论采样点数：}
\begin{equation}
N_i^{theory} = N_{total} \times \frac{|\text{Region}_i|}{|V|} = n \times k \times \frac{|\text{Region}_i|}{|V|}
\end{equation}

\textbf{每个区域的实际采样点数（整数化）：}
\begin{equation}
N_i^{actual} = \text{Round}\left(n \times k \times \frac{|\text{Region}_i|}{|V|}\right)
\end{equation}

\subsection{第三步：区域内LHS采样}

对于区域$\text{Region}_i$，我们需要在其中选择$N_i^{actual}$个节点。

\textbf{区域内节点排序：}
首先将区域内节点按综合中心性评分降序排列：
\begin{equation}
\text{SortedNodes}_i = \text{Sort}(\text{Region}_i, CS, \text{descending})
\end{equation}

\textbf{LHS采样间隔：}
\begin{equation}
\text{Interval}_i = \frac{|\text{Region}_i|}{N_i^{actual}}
\end{equation}

\textbf{LHS采样位置：}
对于第$j$个采样点（$j = 1, 2, \ldots, N_i^{actual}$），其在排序列表中的位置为：
\begin{equation}
\text{Position}_{i,j} = \text{Interval}_i \times (j - 1) + \text{Interval}_i \times U_{i,j}
\end{equation}

其中$U_{i,j} \sim \text{Uniform}(0, 1)$是$[0,1]$区间的均匀分布随机数。

\textbf{选择的节点：}
\begin{equation}
\text{SelectedNode}_{i,j} = \text{SortedNodes}_i[\lfloor \text{Position}_{i,j} \rfloor + 1]
\end{equation}

注意：这里$+1$是因为数组索引从1开始。

\subsection{第四步：构建种子集}

现在我们有了所有采样得到的节点，需要将它们组织成$n$个种子集。

\textbf{全局节点池：}
\begin{equation}
\text{GlobalPool} = \bigcup_{i=0}^{m-1} \{\text{SelectedNode}_{i,j} : j = 1, 2, \ldots, N_i^{actual}\}
\end{equation}

\textbf{种子集构建策略：}
有两种主要策略：

\textbf{策略1：轮询分配}
\begin{equation}
S_s = \{\text{GlobalPool}[s + n \times t] : t = 0, 1, 2, \ldots, k-1\}
\end{equation}
其中$s = 1, 2, \ldots, n$是种子集编号。

\textbf{策略2：区域平衡分配}
对于第$s$个种子集：
\begin{equation}
k_i^{(s)} = \text{Round}\left(k \times \frac{|\text{Region}_i|}{|V|}\right)
\end{equation}

\begin{equation}
S_s = \bigcup_{i=0}^{m-1} \{\text{从区域}i\text{的采样节点中选择}k_i^{(s)}\text{个节点}\}
\end{equation}

\section{LHS采样的完整算法流程}

\subsection{输入参数}
- 网络$G(V,E)$
- 需要生成的种子集数量$n$
- 每个种子集的大小$k$
- 综合中心性评分$CS$

\subsection{输出}
- $n$个种子集$\{S_1, S_2, \ldots, S_n\}$

\subsection{算法步骤}

\textbf{步骤1：网络预处理}
1. 计算网络直径路径
2. 根据公式(1)进行区域划分
3. 计算每个节点的综合中心性评分$CS$

\textbf{步骤2：采样参数计算}
1. 根据公式(2)计算总采样点数
2. 根据公式(4)计算每个区域的实际采样点数

\textbf{步骤3：区域内LHS采样}
对每个区域$i$：
1. 根据公式(5)对区域内节点排序
2. 根据公式(6)计算采样间隔
3. 根据公式(7)和(8)确定采样位置和选择节点

\textbf{步骤4：种子集构建}
1. 根据公式(9)构建全局节点池
2. 根据公式(10)或(11)-(12)构建种子集

\section{关键公式总结}

LHS采样在影响力最大化中的核心公式包括：

\begin{enumerate}
\item \textbf{区域划分：}$\text{Region}_i = \{v \in V : \min_{u \in \text{diameter\_path}} d(v,u) = i\}$

\item \textbf{区域采样数：}$N_i^{actual} = \text{Round}\left(n \times k \times \frac{|\text{Region}_i|}{|V|}\right)$

\item \textbf{采样间隔：}$\text{Interval}_i = \frac{|\text{Region}_i|}{N_i^{actual}}$

\item \textbf{采样位置：}$\text{Position}_{i,j} = \text{Interval}_i \times (j - 1) + \text{Interval}_i \times U_{i,j}$

\item \textbf{节点选择：}$\text{SelectedNode}_{i,j} = \text{SortedNodes}_i[\lfloor \text{Position}_{i,j} \rfloor + 1]$

\item \textbf{种子集构建：}$S_s = \{\text{GlobalPool}[s + n \times t] : t = 0, 1, 2, \ldots, k-1\}$
\end{enumerate}

\section{LHS采样的数学性质}

\subsection{均匀性保证}
LHS采样确保每个区域的采样密度与该区域的节点密度成正比：
\begin{equation}
\frac{N_i^{actual}}{|\text{Region}_i|} \approx \frac{N_{total}}{|V|}
\end{equation}

\subsection{覆盖性保证}
LHS采样保证网络的每个重要区域都有节点被选中，避免了随机采样可能出现的"采样空白"问题。

\subsection{质量保证}
通过在每个区域内按综合中心性评分排序后采样，LHS确保选中的节点具有较高的质量。

\section{与传统采样方法的区别}

\subsection{随机采样}
随机采样直接从$V$中随机选择节点，没有空间结构考虑：
\begin{equation}
S_s^{random} = \{\text{RandomChoice}(V) : \text{重复}k\text{次}\}
\end{equation}

\subsection{均匀采样}
均匀采样在整个网络上等间隔选择：
\begin{equation}
S_s^{uniform} = \{V[\lfloor \frac{|V|}{k} \times (j-1) \rfloor + 1] : j = 1, 2, \ldots, k\}
\end{equation}

\subsection{LHS采样的优势}
LHS采样结合了均匀采样的空间覆盖性和质量导向的节点选择，在保证空间分布均匀的同时，优先选择高质量节点。

\end{document}
